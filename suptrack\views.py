from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import authenticate, login, logout
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.utils import timezone

from .forms import (
    RegistrationForm, 
    LoginForm, 
    SupplyRequestForm, 
    RequestItemFormSet, 
    InventoryForm, 
    IssueStockForm, 
    ReturnStockForm
)
from .models import (
    SupplyRequest, 
    SupplyItem, 
    StockTransaction, 
    RequestItem, 
    Category, 
    QRCode,
    ScanActivity
)
from .decorators import gso_required

def register_view(request):
    if request.method == 'POST':
        form = RegistrationForm(request.POST)
        if form.is_valid():
            form.save()
            return redirect('suptrack:login')
    else:
        form = RegistrationForm()
    return render(request, 'registration/register.html', {'form': form})

def login_view(request):
    if request.method == 'POST':
        form = LoginForm(request.POST)
        if form.is_valid():
            username = form.cleaned_data['username']
            password = form.cleaned_data['password']
            user = authenticate(request, username=username, password=password)
            if user is not None:
                login(request, user)
                # Explicitly check for group membership and redirect
                if user.groups.filter(name='GSO Staff').exists():
                    # Redirect to a new, dedicated GSO dashboard URL
                    return redirect('suptrack:gso_dashboard')
                else:
                    # Redirect other users to the standard department dashboard
                    return redirect('suptrack:department_dashboard')
    else:
        form = LoginForm()
    return render(request, 'registration/login.html', {'form': form})

def logout_view(request):
    logout(request)
    return redirect('suptrack:login')

@login_required
def dashboard(request):
    if request.user.groups.filter(name='GSO Staff').exists():
        return redirect('suptrack:gso_dashboard')
    else:
        return redirect('suptrack:department_dashboard')

@login_required
def gso_dashboard(request):
    if not request.user.groups.filter(name='GSO Staff').exists():
        return redirect('suptrack:department_dashboard')

    pending_requests = SupplyRequest.objects.filter(status='pending').order_by('-requested_date')
    approved_requests = SupplyRequest.objects.filter(status='approved').order_by('-approved_date')[:5]
    low_stock_items = [item for item in SupplyItem.objects.all() if item.is_low_stock]
    
    context = {
        'pending_requests': pending_requests,
        'approved_requests': approved_requests,
        'low_stock_count': len(low_stock_items),
        'is_gso_staff': True
    }
    return render(request, 'dashboard/gso_dashboard.html', context)

@login_required
def department_dashboard(request):
    user_requests = SupplyRequest.objects.filter(requester=request.user).order_by('-requested_date')
    context = {
        'user_requests': user_requests,
        'is_gso_staff': False
    }
    return render(request, 'dashboard/department_dashboard.html', context)

@login_required
def create_supply_request(request):
    if request.method == 'POST':
        form = SupplyRequestForm(request.POST)
        formset = RequestItemFormSet(request.POST)
        if form.is_valid() and formset.is_valid():
            supply_request = form.save(commit=False)
            supply_request.requester = request.user
            supply_request.save()
            formset.instance = supply_request
            formset.save()
            return redirect('suptrack:supply_request_detail', pk=supply_request.pk)
    else:
        form = SupplyRequestForm()
        formset = RequestItemFormSet()
    return render(request, 'supply_request/create_supply_request.html', {'form': form, 'formset': formset})

@login_required
def supply_request_list(request):
    requests = SupplyRequest.objects.filter(requester=request.user)
    return render(request, 'supply_request/supply_request_list.html', {'requests': requests})

@login_required
def supply_request_detail(request, pk):
    supply_request = get_object_or_404(SupplyRequest, pk=pk, requester=request.user)
    if request.method == 'POST':
        formset = RequestItemFormSet(request.POST, instance=supply_request)
        if formset.is_valid():
            formset.save()
            return redirect('suptrack:supply_request_detail', pk=supply_request.pk)
    else:
        formset = RequestItemFormSet(instance=supply_request)
    return render(request, 'supply_request/supply_request_detail.html', {'supply_request': supply_request, 'formset': formset})

@login_required
@gso_required
def review_supply_request(request, pk):
    supply_request = get_object_or_404(SupplyRequest, pk=pk)
    RequestItemFormSet = forms.inlineformset_factory(SupplyRequest, RequestItem, fields=('supply_item', 'quantity_requested'), extra=0, can_delete=False)

    if request.method == 'POST':
        status = request.POST.get('status')

        if status == 'approved':
            can_approve = True
            for item in supply_request.request_items.all():
                if item.quantity_requested > item.supply_item.current_stock:
                    can_approve = False
                    messages.error(request, f'Insufficient stock for {item.supply_item.name}. Requested: {item.quantity_requested}, Available: {item.supply_item.current_stock}')
                    break
            
            if can_approve:
                for item in supply_request.request_items.all():
                    supply_item_obj = item.supply_item
                    new_stock_level = supply_item_obj.current_stock - item.quantity_requested
                    supply_item_obj.current_stock = new_stock_level
                    supply_item_obj.save()

                    # Create a stock transaction log
                    StockTransaction.objects.create(
                        supply_item=supply_item_obj,
                        transaction_type='fulfillment',
                        quantity_change=-item.quantity_requested,
                        new_quantity_on_hand=new_stock_level,
                        performed_by=request.user,
                        reference_document=f'Request ID: {supply_request.request_id}',
                        request_item=item,
                        notes=f'Fulfilled request {supply_request.request_id} for {item.quantity_requested} units.'
                    )
                
                supply_request.status = 'approved'
                supply_request.approved_by = request.user
                supply_request.approved_date = timezone.now()
                supply_request.save()
                messages.success(request, f'Request {supply_request.request_id} has been approved and stock levels updated.')
                return redirect('suptrack:dashboard')

        elif status == 'rejected':
            rejection_reason = request.POST.get('rejection_reason', '').strip()
            if not rejection_reason:
                messages.error(request, 'A reason is required for rejection.')
            else:
                supply_request.status = 'rejected'
                supply_request.rejection_reason = rejection_reason
                supply_request.save()
                messages.info(request, f'Request {supply_request.request_id} has been rejected.')
                return redirect('suptrack:dashboard')

    formset = RequestItemFormSet(instance=supply_request)
    return render(request, 'supply_request/review_supply_request.html', {'supply_request': supply_request, 'formset': formset})

@login_required
@gso_required
def inventory_dashboard(request):
    items = SupplyItem.objects.all().order_by('name')
    categories = Category.objects.all()

    # Search and Filter Logic
    query = request.GET.get('q')
    category_filter = request.GET.get('category')
    stock_status_filter = request.GET.get('stock_status')

    if query:
        items = items.filter(name__icontains=query)
    
    if category_filter:
        items = items.filter(category__id=category_filter)

    if stock_status_filter == 'low_stock':
        items = [item for item in items if item.is_low_stock]
    elif stock_status_filter == 'in_stock':
        items = [item for item in items if not item.is_low_stock]

    if request.method == 'POST':
        form = InventoryForm(request.POST)
        if form.is_valid():
            supply_item = form.save()
            StockTransaction.objects.create(
                supply_item=supply_item,
                transaction_type='initial_stock',
                quantity_change=supply_item.current_stock,
                new_quantity_on_hand=supply_item.current_stock,
                performed_by=request.user,
                notes='Initial stock added for new item.'
            )
            messages.success(request, f'New item "{supply_item.name}" added to inventory.')
            # Return the updated list, preserving filters
            items = SupplyItem.objects.all().order_by('name') # Re-fetch to include the new item
            return render(request, 'inventory/partials/inventory_list.html', {'items': items})
    else:
        form = InventoryForm()

    context = {
        'items': items,
        'form': form,
        'categories': categories,
        'selected_category': category_filter,
        'selected_stock_status': stock_status_filter,
        'search_query': query,
    }

    if request.htmx:
        return render(request, 'inventory/partials/inventory_list.html', context)

    return render(request, 'inventory/inventory_dashboard.html', context)

@login_required
@gso_required
def low_stock_alert(request):
    low_stock_items = [item for item in SupplyItem.objects.all() if item.is_low_stock]
    context = {
        'low_stock_items': low_stock_items,
    }
    return render(request, 'inventory/low_stock_alert.html', context)

@login_required
@gso_required
def item_detail(request, pk):
    item = get_object_or_404(SupplyItem, pk=pk)
    transactions = StockTransaction.objects.filter(supply_item=item).order_by('-timestamp')
    try:
        qr_code = item.qr_code
    except SupplyItem.qr_code.RelatedObjectDoesNotExist:
        qr_code = None
    return render(request, 'inventory/item_detail.html', {'item': item, 'transactions': transactions, 'qr_code': qr_code})

@login_required
@gso_required
def edit_item(request, pk):
    item = get_object_or_404(SupplyItem, pk=pk)
    original_stock = item.current_stock

    if request.method == 'POST':
        form = InventoryForm(request.POST, instance=item)
        if form.is_valid():
            updated_item = form.save()
            new_stock = updated_item.current_stock

            if original_stock != new_stock:
                quantity_change = new_stock - original_stock
                StockTransaction.objects.create(
                    supply_item=updated_item,
                    transaction_type='adjustment',
                    quantity_change=quantity_change,
                    new_quantity_on_hand=new_stock,
                    performed_by=request.user,
                    notes=f'Manual stock adjustment by {request.user.username}.'
                )

            messages.success(request, f'Item "{item.name}" has been updated.')
            return redirect('suptrack:item_detail', pk=item.pk)
    else:
        form = InventoryForm(instance=item)
    return render(request, 'inventory/edit_item.html', {'form': form, 'item': item})

@login_required
def qr_scanner(request):
    return render(request, 'inventory/qr_scanner.html')

@login_required
def process_qr_scan(request, item_code):
    try:
        supply_item = SupplyItem.objects.get(item_code=item_code)
        qr_code = supply_item.qr_code
        
        ScanActivity.objects.create(
            qr_code=qr_code,
            scanned_by=request.user,
            scan_type='general',
            success=True
        )
        
        return redirect('suptrack:item_detail', pk=supply_item.pk)
    except (SupplyItem.DoesNotExist, QRCode.DoesNotExist):
        messages.error(request, 'Invalid QR Code. Item not found.')
        return redirect('suptrack:qr_scanner')

@login_required
@gso_required
def issue_stock(request, pk):
    item = get_object_or_404(SupplyItem, pk=pk)
    if request.method == 'POST':
        form = IssueStockForm(request.POST)
        if form.is_valid():
            quantity = form.cleaned_data['quantity']
            notes = form.cleaned_data['notes']

            if quantity > item.current_stock:
                messages.error(request, 'Cannot issue more stock than is available.')
                return redirect('suptrack:item_detail', pk=item.pk)

            item.current_stock -= quantity
            item.save()

            StockTransaction.objects.create(
                supply_item=item,
                transaction_type='fulfillment',
                quantity_change=-quantity,
                new_quantity_on_hand=item.current_stock,
                performed_by=request.user,
                notes=notes
            )
            messages.success(request, f'{quantity} units of {item.name} issued successfully.')
            return redirect('suptrack:item_detail', pk=item.pk)
    return redirect('suptrack:item_detail', pk=item.pk)

@login_required
@gso_required
def return_stock(request, pk):
    item = get_object_or_404(SupplyItem, pk=pk)
    if request.method == 'POST':
        form = ReturnStockForm(request.POST)
        if form.is_valid():
            quantity = form.cleaned_data['quantity']
            notes = form.cleaned_data['notes']

            item.current_stock += quantity
            item.save()

            StockTransaction.objects.create(
                supply_item=item,
                transaction_type='return',
                quantity_change=quantity,
                new_quantity_on_hand=item.current_stock,
                performed_by=request.user,
                notes=notes
            )
            messages.success(request, f'{quantity} units of {item.name} returned to stock.')
            return redirect('suptrack:item_detail', pk=item.pk)
    return redirect('suptrack:item_detail', pk=item.pk)

@login_required
@gso_required
def audit_item(request, item_code):
    try:
        supply_item = SupplyItem.objects.get(item_code=item_code)
        qr_code = supply_item.qr_code

        ScanActivity.objects.create(
            qr_code=qr_code,
            scanned_by=request.user,
            scan_type='audit',
            success=True
        )

        return render(request, 'inventory/audit_item.html', {'item': supply_item})
    except (SupplyItem.DoesNotExist, QRCode.DoesNotExist):
        messages.error(request, 'Invalid QR Code. Item not found.')
        return redirect('suptrack:qr_scanner')

@login_required
@gso_required
def process_audit(request, pk):
    item = get_object_or_404(SupplyItem, pk=pk)
    if request.method == 'POST':
        audited_quantity = int(request.POST.get('audited_quantity'))
        notes = request.POST.get('notes', '')

        discrepancy = audited_quantity - item.current_stock

        if discrepancy != 0:
            item.current_stock = audited_quantity
            item.save()

            StockTransaction.objects.create(
                supply_item=item,
                transaction_type='adjustment',
                quantity_change=discrepancy,
                new_quantity_on_hand=item.current_stock,
                performed_by=request.user,
                notes=f"Inventory audit. Discrepancy of {discrepancy}. {notes}"
            )
            messages.warning(request, f'Stock for {item.name} adjusted by {discrepancy}.')
        else:
            messages.success(request, f'Stock for {item.name} verified. No discrepancy found.')

        return redirect('suptrack:item_detail', pk=item.pk)
    return redirect('suptrack:item_detail', pk=item.pk)