{% extends 'base.html' %}

{% block title %}Item Details: {{ item.name }}{% endblock %}

{% block content %}
<div class="bg-white p-8 rounded-lg shadow-lg">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-800">{{ item.name }}</h1>
        <a href="{% url 'suptrack:inventory_dashboard' %}" class="text-blue-500 hover:text-blue-700 font-semibold">&larr; Back to Inventory</a>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
            <h2 class="text-xl font-semibold text-gray-700">Item Information</h2>
            <p><strong>Category:</strong> {{ item.category.name }}</p>
            <p><strong>Unit of Measure:</strong> {{ item.get_unit_of_measure_display }}</p>
            <p><strong>Description:</strong> {{ item.description|default:"No description provided." }}</p>
        </div>
        <div>
            <h2 class="text-xl font-semibold text-gray-700">Stock Details</h2>
            <p><strong>Quantity on Hand:</strong> <span class="font-bold text-2xl {% if item.quantity_on_hand <= item.reorder_level %}text-red-500{% else %}text-green-600{% endif %}">{{ item.quantity_on_hand }}</span></p>
            <p><strong>Reorder Level:</strong> {{ item.reorder_level }}</p>
        </div>
    </div>

    {% if user.userprofile.role == 'gso_staff' %}
    <div class="mt-8 grid grid-cols-1 md:grid-cols-2 gap-8">
        <!-- Issue Stock Form -->
        <div>
            <h3 class="text-xl font-bold text-gray-900 mb-4">Issue Stock</h3>
            <form action="{% url 'suptrack:issue_stock' item.pk %}" method="post" class="bg-white p-6 rounded-lg shadow-md">
                {% csrf_token %}
                <div class="mb-4">
                    <label for="id_issue_quantity" class="block text-sm font-medium text-gray-700">Quantity</label>
                    <input type="number" name="quantity" id="id_issue_quantity" min="1" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" required>
                </div>
                <div class="mb-4">
                    <label for="id_issue_notes" class="block text-sm font-medium text-gray-700">Notes</label>
                    <textarea name="notes" id="id_issue_notes" rows="3" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"></textarea>
                </div>
                <button type="submit" class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700">Issue Stock</button>
            </form>
        </div>

        <!-- Return Stock Form -->
        <div>
            <h3 class="text-xl font-bold text-gray-900 mb-4">Return Stock</h3>
            <form action="{% url 'suptrack:return_stock' item.pk %}" method="post" class="bg-white p-6 rounded-lg shadow-md">
                {% csrf_token %}
                <div class="mb-4">
                    <label for="id_return_quantity" class="block text-sm font-medium text-gray-700">Quantity</label>
                    <input type="number" name="quantity" id="id_return_quantity" min="1" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" required>
                </div>
                <div class="mb-4">
                    <label for="id_return_notes" class="block text-sm font-medium text-gray-700">Notes</label>
                    <textarea name="notes" id="id_return_notes" rows="3" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"></textarea>
                </div>
                <button type="submit" class="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700">Return Stock</button>
            </form>
        </div>
    </div>
    {% endif %}

    <div class="mt-8">
        <h3 class="text-xl font-bold text-gray-900 mb-4">Transaction History</h3>
        <div class="overflow-x-auto bg-white rounded-lg shadow">
            <table class="min-w-full leading-normal">
                <thead>
                    <tr>
                        <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                            Date
                        </th>
                        <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                            Type
                        </th>
                        <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider">
                            Quantity Change
                        </th>
                        <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider">
                            New Stock
                        </th>
                        <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                            User
                        </th>
                        <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                            Notes
                        </th>
                    </tr>
                </thead>
                <tbody>
                    {% for tx in transactions %}
                    <tr>
                        <td class="px-5 py-4 border-b border-gray-200 bg-white text-sm">
                            <p class="text-gray-900 whitespace-no-wrap">{{ tx.timestamp|date:"Y-m-d H:i" }}</p>
                        </td>
                        <td class="px-5 py-4 border-b border-gray-200 bg-white text-sm">
                            <span class="relative inline-block px-3 py-1 font-semibold text-green-900 leading-tight">
                                <span aria-hidden class="absolute inset-0 bg-green-200 opacity-50 rounded-full"></span>
                                <span class="relative">{{ tx.get_transaction_type_display }}</span>
                            </span>
                        </td>
                        <td class="px-5 py-4 border-b border-gray-200 bg-white text-sm text-right">
                            <p class="text-gray-900 whitespace-no-wrap">{{ tx.quantity_change }}</p>
                        </td>
                        <td class="px-5 py-4 border-b border-gray-200 bg-white text-sm text-right">
                            <p class="text-gray-900 whitespace-no-wrap">{{ tx.new_quantity_on_hand }}</p>
                        </td>
                        <td class="px-5 py-4 border-b border-gray-200 bg-white text-sm">
                            <p class="text-gray-900 whitespace-no-wrap">{{ tx.performed_by.username }}</p>
                        </td>
                        <td class="px-5 py-4 border-b border-gray-200 bg-white text-sm">
                            <p class="text-gray-600 whitespace-no-wrap">{{ tx.notes }}</p>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="6" class="text-center py-10 px-5 text-gray-500">
                            No transactions found for this item.
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <div class="mt-8">
        <h3 class="text-xl font-bold text-gray-900 mb-4">Item QR Code</h3>
        <div class="flex flex-col items-center justify-center bg-white p-4 rounded-lg shadow-md">
            {% if qr_code %}
                <img src="{{ qr_code.qr_image.url }}" alt="QR Code for {{ item.name }}" class="w-48 h-48">
                <button onclick="window.print()" class="mt-4 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700">Print QR Code</button>
            {% else %}
                <p class="text-gray-500">QR code not yet generated.</p>
            {% endif %}
        </div>
    </div>

    <div class="mt-8 flex justify-end">
        <a href="{% url 'suptrack:edit_item' item.pk %}" class="bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700">Edit Item</a>
    </div>
</div>
{% endblock %}
