INFO 2025-07-24 21:49:06,654 autoreload 10100 23044 Watching for file changes with StatReloader
INFO 2025-07-24 21:49:11,136 basehttp 10100 5200 "GET /admin/?ide_webview_request_time=1753364951068 HTTP/1.1" 302 0
INFO 2025-07-24 21:49:11,457 basehttp 10100 5200 "GET /admin/login/?next=/admin/%3Fide_webview_request_time%3D1753364951068 HTTP/1.1" 200 4255
INFO 2025-07-24 21:49:11,841 basehttp 10100 20892 "GET /static/admin/css/login.css HTTP/1.1" 200 951
INFO 2025-07-24 21:49:11,905 basehttp 10100 15500 "GET /static/admin/css/dark_mode.css HTTP/1.1" 200 2808
INFO 2025-07-24 21:49:11,916 basehttp 10100 5200 "GET /static/admin/css/base.css HTTP/1.1" 200 22120
INFO 2025-07-24 21:49:11,921 basehttp 10100 15608 "GET /static/admin/css/responsive.css HTTP/1.1" 200 16565
INFO 2025-07-24 21:49:11,929 basehttp 10100 24428 "GET /static/admin/css/nav_sidebar.css HTTP/1.1" 200 2810
INFO 2025-07-24 21:49:11,931 basehttp 10100 25320 "GET /static/admin/js/theme.js HTTP/1.1" 200 1653
INFO 2025-07-24 21:49:11,937 basehttp 10100 25320 "GET /static/admin/js/nav_sidebar.js HTTP/1.1" 200 3063
WARNING 2025-07-24 21:49:12,056 log 10100 25320 Not Found: /@vite/client
WARNING 2025-07-24 21:49:12,057 basehttp 10100 25320 "GET /@vite/client HTTP/1.1" 404 2555
INFO 2025-07-24 21:49:29,855 basehttp 10100 14272 "POST /admin/login/?next=/admin/%3Fide_webview_request_time%3D1753364951068 HTTP/1.1" 302 0
INFO 2025-07-24 21:49:29,866 middleware 10100 14272 New session started for user: admin from IP: 127.0.0.1
INFO 2025-07-24 21:49:29,903 basehttp 10100 14272 "GET /admin/?ide_webview_request_time=1753364951068 HTTP/1.1" 200 11693
INFO 2025-07-24 21:49:29,919 basehttp 10100 14272 "GET /static/admin/css/dashboard.css HTTP/1.1" 200 441
INFO 2025-07-24 21:49:29,950 basehttp 10100 14272 "GET /static/admin/img/icon-addlink.svg HTTP/1.1" 200 331
INFO 2025-07-24 21:49:29,951 basehttp 10100 16204 "GET /static/admin/img/icon-changelink.svg HTTP/1.1" 200 380
ERROR 2025-07-24 21:49:30,002 middleware 10100 16204 User without profile attempted access: admin
ERROR 2025-07-24 21:49:30,104 log 10100 16204 Internal Server Error: /@vite/client
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\suptrack\middleware.py", line 52, in process_request
    profile = request.user.userprofile
              ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\utils\functional.py", line 253, in inner
    return func(_wrapped, *args)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\models\fields\related_descriptors.py", line 534, in __get__
    raise self.RelatedObjectDoesNotExist(
    ...<2 lines>...
    )
django.contrib.auth.models.User.userprofile.RelatedObjectDoesNotExist: User has no userprofile.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\contrib\messages\api.py", line 27, in add_message
    messages = request._messages
               ^^^^^^^^^^^^^^^^^
AttributeError: 'WSGIRequest' object has no attribute '_messages'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\utils\deprecation.py", line 119, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\middleware.py", line 73, in process_request
    messages.error(
    ~~~~~~~~~~~~~~^
        request,
        ^^^^^^^^
        "User profile not found. Please contact an administrator."
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\contrib\messages\api.py", line 121, in error
    add_message(
    ~~~~~~~~~~~^
        request,
        ^^^^^^^^
    ...<3 lines>...
        fail_silently=fail_silently,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\contrib\messages\api.py", line 35, in add_message
    raise MessageFailure(
    ...<2 lines>...
    )
django.contrib.messages.api.MessageFailure: You cannot add messages without installing django.contrib.messages.middleware.MessageMiddleware
ERROR 2025-07-24 21:49:30,122 basehttp 10100 16204 "GET /@vite/client HTTP/1.1" 500 96470
INFO 2025-07-24 22:02:21,999 autoreload 13748 17360 Watching for file changes with StatReloader
INFO 2025-07-24 22:02:30,151 basehttp 13748 13944 - Broken pipe from ('127.0.0.1', 54711)
INFO 2025-07-24 22:02:30,167 basehttp 13748 14072 "GET /admin/?ide_webview_request_time=1753364951068 HTTP/1.1" 200 11043
ERROR 2025-07-24 22:02:30,448 middleware 13748 14072 User without profile attempted access: admin
ERROR 2025-07-24 22:02:30,481 log 13748 14072 Internal Server Error: /@vite/client
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\suptrack\middleware.py", line 52, in process_request
    profile = request.user.userprofile
              ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\utils\functional.py", line 253, in inner
    return func(_wrapped, *args)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\models\fields\related_descriptors.py", line 534, in __get__
    raise self.RelatedObjectDoesNotExist(
    ...<2 lines>...
    )
django.contrib.auth.models.User.userprofile.RelatedObjectDoesNotExist: User has no userprofile.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\contrib\messages\api.py", line 27, in add_message
    messages = request._messages
               ^^^^^^^^^^^^^^^^^
AttributeError: 'WSGIRequest' object has no attribute '_messages'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\utils\deprecation.py", line 119, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\middleware.py", line 73, in process_request
    messages.error(
    ~~~~~~~~~~~~~~^
        request,
        ^^^^^^^^
        "User profile not found. Please contact an administrator."
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\contrib\messages\api.py", line 121, in error
    add_message(
    ~~~~~~~~~~~^
        request,
        ^^^^^^^^
    ...<3 lines>...
        fail_silently=fail_silently,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\contrib\messages\api.py", line 35, in add_message
    raise MessageFailure(
    ...<2 lines>...
    )
django.contrib.messages.api.MessageFailure: You cannot add messages without installing django.contrib.messages.middleware.MessageMiddleware
ERROR 2025-07-24 22:02:30,489 basehttp 13748 14072 "GET /@vite/client HTTP/1.1" 500 96470
INFO 2025-07-24 22:11:28,061 autoreload 13748 17360 C:\Users\<USER>\dev\smartsupply\suptrack\middleware.py changed, reloading.
INFO 2025-07-24 22:11:29,278 autoreload 25056 9268 Watching for file changes with StatReloader
INFO 2025-07-24 22:11:40,012 autoreload 11948 22800 Watching for file changes with StatReloader
ERROR 2025-07-24 22:11:44,668 middleware 11948 2276 Exception for user admin: NoReverseMatch: Reverse for 'dashboard' not found. 'dashboard' is not a valid view function or pattern name.
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\views.py", line 9, in dashboard
    return render(request, 'dashboard.html', context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'dashboard' not found. 'dashboard' is not a valid view function or pattern name.
ERROR 2025-07-24 22:11:44,761 log 11948 2276 Internal Server Error: /
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\views.py", line 9, in dashboard
    return render(request, 'dashboard.html', context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'dashboard' not found. 'dashboard' is not a valid view function or pattern name.
ERROR 2025-07-24 22:11:44,767 basehttp 11948 2276 "GET /?ide_webview_request_time=1753366304628 HTTP/1.1" 500 143699
WARNING 2025-07-24 22:11:44,960 log 11948 2276 Not Found: /@vite/client
WARNING 2025-07-24 22:11:44,964 basehttp 11948 2276 "GET /@vite/client HTTP/1.1" 404 2806
INFO 2025-07-24 22:12:12,106 autoreload 15400 1952 Watching for file changes with StatReloader
INFO 2025-07-24 22:12:17,288 basehttp 15400 8800 "GET /?ide_webview_request_time=1753366337246 HTTP/1.1" 200 33013
INFO 2025-07-24 22:12:17,407 basehttp 15400 15756 "GET /static/js/dashboard.js HTTP/1.1" 200 4064
INFO 2025-07-24 22:12:17,410 basehttp 15400 8800 "GET /static/css/dashboard.css HTTP/1.1" 200 1251
WARNING 2025-07-24 22:12:20,236 log 15400 8800 Not Found: /@vite/client
WARNING 2025-07-24 22:12:20,237 basehttp 15400 8800 "GET /@vite/client HTTP/1.1" 404 2806
INFO 2025-07-24 22:15:06,249 basehttp 15400 24516 "GET /?ide_webview_request_time=1753366337246 HTTP/1.1" 200 33013
INFO 2025-07-24 22:15:06,282 basehttp 15400 24516 "GET /?ide_webview_request_time=1753366337246 HTTP/1.1" 200 33013
INFO 2025-07-24 22:15:06,355 basehttp 15400 22212 "GET /static/js/dashboard.js HTTP/1.1" 304 0
INFO 2025-07-24 22:15:06,355 basehttp 15400 24516 "GET /static/css/dashboard.css HTTP/1.1" 304 0
WARNING 2025-07-24 22:15:06,889 log 15400 24516 Not Found: /@vite/client
WARNING 2025-07-24 22:15:06,889 basehttp 15400 24516 "GET /@vite/client HTTP/1.1" 404 2806
INFO 2025-07-24 23:29:52,086 autoreload 15820 13316 Watching for file changes with StatReloader
INFO 2025-07-24 23:30:07,198 basehttp 15820 15492 "GET / HTTP/1.1" 200 33013
INFO 2025-07-24 23:30:08,217 basehttp 15820 15492 "GET /static/css/dashboard.css HTTP/1.1" 200 1251
INFO 2025-07-24 23:30:08,225 basehttp 15820 15252 "GET /static/js/dashboard.js HTTP/1.1" 200 4064
WARNING 2025-07-24 23:30:11,937 log 15820 11964 Not Found: /favicon.ico
WARNING 2025-07-24 23:30:11,968 basehttp 15820 11964 "GET /favicon.ico HTTP/1.1" 404 2803
INFO 2025-07-24 23:36:02,592 autoreload 15820 13316 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-24 23:36:03,771 autoreload 22920 2964 Watching for file changes with StatReloader
INFO 2025-07-24 23:36:15,346 autoreload 22920 2964 C:\Users\<USER>\dev\smartsupply\suptrack\urls.py changed, reloading.
INFO 2025-07-24 23:36:16,161 autoreload 22168 2636 Watching for file changes with StatReloader
INFO 2025-07-24 23:37:21,633 autoreload 22168 2636 C:\Users\<USER>\dev\smartsupply\suptrack\forms.py changed, reloading.
INFO 2025-07-24 23:37:22,557 autoreload 18624 24968 Watching for file changes with StatReloader
INFO 2025-07-24 23:39:54,672 autoreload 18624 24968 C:\Users\<USER>\dev\smartsupply\suptrack\forms.py changed, reloading.
INFO 2025-07-24 23:39:55,469 autoreload 24920 25288 Watching for file changes with StatReloader
INFO 2025-07-24 23:40:22,246 autoreload 24920 25288 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-24 23:40:23,063 autoreload 15140 4764 Watching for file changes with StatReloader
INFO 2025-07-24 23:41:32,708 autoreload 15140 4764 C:\Users\<USER>\dev\smartsupply\suptrack\models.py changed, reloading.
INFO 2025-07-24 23:41:33,874 autoreload 23768 13648 Watching for file changes with StatReloader
INFO 2025-07-24 23:41:59,646 autoreload 23516 25932 Watching for file changes with StatReloader
INFO 2025-07-24 23:47:16,078 basehttp 23768 7152 "GET / HTTP/1.1" 302 0
INFO 2025-07-24 23:47:16,145 basehttp 23768 7152 "GET /login/?next=/ HTTP/1.1" 200 16568
INFO 2025-07-24 23:47:16,548 basehttp 23768 7152 "GET /static/css/dashboard.css HTTP/1.1" 200 1251
INFO 2025-07-24 23:47:16,555 basehttp 23768 6208 "GET /static/js/dashboard.js HTTP/1.1" 200 4064
WARNING 2025-07-24 23:47:16,690 log 23768 6208 Not Found: /favicon.ico
WARNING 2025-07-24 23:47:16,693 basehttp 23768 6208 "GET /favicon.ico HTTP/1.1" 404 4421
INFO 2025-07-24 23:47:20,707 basehttp 23768 6208 "GET / HTTP/1.1" 302 0
INFO 2025-07-24 23:47:20,718 basehttp 23768 6208 "GET /login/?next=/ HTTP/1.1" 200 16568
INFO 2025-07-24 23:47:26,345 basehttp 23768 6208 "GET /register/ HTTP/1.1" 200 17903
INFO 2025-07-24 23:48:00,300 basehttp 23768 6208 "POST /register/ HTTP/1.1" 200 18103
INFO 2025-07-24 23:48:11,953 basehttp 23768 6208 "GET /login/ HTTP/1.1" 200 16568
INFO 2025-07-24 23:48:20,632 basehttp 23768 6208 "POST /login/ HTTP/1.1" 302 0
INFO 2025-07-24 23:48:20,645 middleware 23768 6208 New session started for user: lanzy from IP: 127.0.0.1
INFO 2025-07-24 23:48:20,661 basehttp 23768 6208 "GET / HTTP/1.1" 200 33013
INFO 2025-07-24 23:52:01,369 autoreload 23768 13648 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-24 23:52:01,676 autoreload 23516 25932 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-24 23:52:02,296 autoreload 16284 4196 Watching for file changes with StatReloader
INFO 2025-07-24 23:52:02,631 autoreload 11396 8788 Watching for file changes with StatReloader
INFO 2025-07-24 23:52:52,674 autoreload 3688 11240 Watching for file changes with StatReloader
INFO 2025-07-24 23:52:58,331 basehttp 16284 23804 "GET / HTTP/1.1" 302 0
INFO 2025-07-24 23:52:58,407 basehttp 16284 23804 "GET /login/?next=/ HTTP/1.1" 200 12878
INFO 2025-07-24 23:52:58,610 basehttp 16284 23804 "GET /static/css/dashboard.css HTTP/1.1" 304 0
INFO 2025-07-24 23:52:58,616 basehttp 16284 25152 "GET /static/js/dashboard.js HTTP/1.1" 304 0
INFO 2025-07-24 23:53:06,142 basehttp 16284 25152 "POST /login/?next=/ HTTP/1.1" 302 0
INFO 2025-07-24 23:53:06,156 middleware 16284 25152 New session started for user: lanzy from IP: 127.0.0.1
INFO 2025-07-24 23:53:06,202 basehttp 16284 25152 "GET / HTTP/1.1" 200 13248
INFO 2025-07-24 23:53:10,187 basehttp 16284 26200 "GET /supply-request/new/ HTTP/1.1" 200 16056
INFO 2025-07-24 23:53:12,531 basehttp 16284 26200 "GET /supply-request/ HTTP/1.1" 200 13283
INFO 2025-07-24 23:53:14,755 basehttp 16284 26200 "GET / HTTP/1.1" 200 13248
INFO 2025-07-24 23:53:16,018 basehttp 16284 26200 "GET /supply-request/new/ HTTP/1.1" 200 16056
INFO 2025-07-24 23:53:18,139 basehttp 16284 14624 "GET /supply-request/ HTTP/1.1" 200 13283
INFO 2025-07-24 23:55:19,414 autoreload 16284 4196 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-24 23:55:19,464 autoreload 3688 11240 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-24 23:55:19,901 autoreload 11396 8788 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-24 23:55:20,603 autoreload 15560 24424 Watching for file changes with StatReloader
INFO 2025-07-24 23:55:20,605 autoreload 11204 11808 Watching for file changes with StatReloader
INFO 2025-07-24 23:55:21,160 autoreload 25852 6964 Watching for file changes with StatReloader
INFO 2025-07-24 23:55:45,142 autoreload 25852 6964 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-24 23:55:45,265 autoreload 15560 24424 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-24 23:55:45,448 autoreload 11204 11808 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-24 23:55:46,231 autoreload 26420 2292 Watching for file changes with StatReloader
INFO 2025-07-24 23:55:46,284 autoreload 26416 14120 Watching for file changes with StatReloader
INFO 2025-07-24 23:55:46,554 autoreload 22484 9080 Watching for file changes with StatReloader
INFO 2025-07-24 23:55:55,854 autoreload 26420 2292 C:\Users\<USER>\dev\smartsupply\suptrack\urls.py changed, reloading.
INFO 2025-07-24 23:55:56,061 autoreload 22484 9080 C:\Users\<USER>\dev\smartsupply\suptrack\urls.py changed, reloading.
INFO 2025-07-24 23:55:56,120 autoreload 26416 14120 C:\Users\<USER>\dev\smartsupply\suptrack\urls.py changed, reloading.
INFO 2025-07-24 23:55:56,828 autoreload 21820 7868 Watching for file changes with StatReloader
INFO 2025-07-24 23:55:57,068 autoreload 21428 24456 Watching for file changes with StatReloader
INFO 2025-07-24 23:55:57,264 autoreload 23096 18912 Watching for file changes with StatReloader
INFO 2025-07-24 23:56:38,816 autoreload 23096 18912 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-24 23:56:39,196 autoreload 21428 24456 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-24 23:56:39,696 autoreload 21820 7868 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-24 23:56:39,866 autoreload 20000 22244 Watching for file changes with StatReloader
INFO 2025-07-24 23:56:40,183 autoreload 5492 26392 Watching for file changes with StatReloader
INFO 2025-07-24 23:56:40,565 autoreload 3916 21612 Watching for file changes with StatReloader
INFO 2025-07-24 23:56:57,752 autoreload 20000 22244 C:\Users\<USER>\dev\smartsupply\suptrack\models.py changed, reloading.
INFO 2025-07-24 23:56:57,828 autoreload 5492 26392 C:\Users\<USER>\dev\smartsupply\suptrack\models.py changed, reloading.
INFO 2025-07-24 23:56:58,131 autoreload 3916 21612 C:\Users\<USER>\dev\smartsupply\suptrack\models.py changed, reloading.
INFO 2025-07-24 23:56:58,869 autoreload 25176 16084 Watching for file changes with StatReloader
INFO 2025-07-24 23:56:58,920 autoreload 16528 23480 Watching for file changes with StatReloader
INFO 2025-07-24 23:56:59,126 autoreload 16376 8456 Watching for file changes with StatReloader
INFO 2025-07-24 23:57:12,470 autoreload 25176 16084 C:\Users\<USER>\dev\smartsupply\suptrack\models.py changed, reloading.
INFO 2025-07-24 23:57:12,620 autoreload 16376 8456 C:\Users\<USER>\dev\smartsupply\suptrack\models.py changed, reloading.
INFO 2025-07-24 23:57:12,796 autoreload 16528 23480 C:\Users\<USER>\dev\smartsupply\suptrack\models.py changed, reloading.
INFO 2025-07-24 23:57:13,592 autoreload 19696 24980 Watching for file changes with StatReloader
INFO 2025-07-24 23:57:13,703 autoreload 10528 16644 Watching for file changes with StatReloader
INFO 2025-07-24 23:57:14,079 autoreload 19728 17024 Watching for file changes with StatReloader
INFO 2025-07-24 23:58:26,523 autoreload 19696 24980 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-24 23:58:26,596 autoreload 19728 17024 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-24 23:58:27,343 autoreload 9500 24968 Watching for file changes with StatReloader
INFO 2025-07-24 23:58:27,501 autoreload 22384 7392 Watching for file changes with StatReloader
INFO 2025-07-24 23:58:37,193 autoreload 22384 7392 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-24 23:58:37,848 autoreload 9500 24968 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-24 23:58:38,322 autoreload 12968 21816 Watching for file changes with StatReloader
INFO 2025-07-24 23:58:38,917 autoreload 15336 13056 Watching for file changes with StatReloader
INFO 2025-07-24 23:58:49,866 autoreload 12968 21816 C:\Users\<USER>\dev\smartsupply\suptrack\forms.py changed, reloading.
INFO 2025-07-24 23:58:50,416 autoreload 15336 13056 C:\Users\<USER>\dev\smartsupply\suptrack\forms.py changed, reloading.
INFO 2025-07-24 23:58:51,026 autoreload 23408 13164 Watching for file changes with StatReloader
INFO 2025-07-24 23:58:51,265 autoreload 25252 2304 Watching for file changes with StatReloader
INFO 2025-07-25 00:01:03,763 autoreload 23408 13164 C:\Users\<USER>\dev\smartsupply\suptrack\models.py changed, reloading.
INFO 2025-07-25 00:01:03,819 autoreload 25252 2304 C:\Users\<USER>\dev\smartsupply\suptrack\models.py changed, reloading.
INFO 2025-07-25 00:01:04,689 autoreload 12636 26044 Watching for file changes with StatReloader
INFO 2025-07-25 00:01:04,813 autoreload 22224 23860 Watching for file changes with StatReloader
INFO 2025-07-25 00:01:37,690 autoreload 22224 23860 C:\Users\<USER>\dev\smartsupply\suptrack\models.py changed, reloading.
INFO 2025-07-25 00:01:38,449 autoreload 8776 21924 Watching for file changes with StatReloader
INFO 2025-07-25 00:01:38,504 autoreload 12636 26044 C:\Users\<USER>\dev\smartsupply\suptrack\models.py changed, reloading.
INFO 2025-07-25 00:01:39,004 autoreload 5532 16088 Watching for file changes with StatReloader
INFO 2025-07-25 00:02:38,989 autoreload 8776 21924 C:\Users\<USER>\dev\smartsupply\suptrack\models.py changed, reloading.
INFO 2025-07-25 00:02:39,096 autoreload 5532 16088 C:\Users\<USER>\dev\smartsupply\suptrack\models.py changed, reloading.
INFO 2025-07-25 00:02:40,492 autoreload 24872 16892 Watching for file changes with StatReloader
INFO 2025-07-25 00:02:40,576 autoreload 5432 25572 Watching for file changes with StatReloader
INFO 2025-07-25 00:02:54,378 autoreload 24872 16892 C:\Users\<USER>\dev\smartsupply\suptrack\models.py changed, reloading.
INFO 2025-07-25 00:02:54,629 autoreload 5432 25572 C:\Users\<USER>\dev\smartsupply\suptrack\models.py changed, reloading.
INFO 2025-07-25 00:02:55,573 autoreload 23624 16172 Watching for file changes with StatReloader
INFO 2025-07-25 00:02:56,155 autoreload 3700 2276 Watching for file changes with StatReloader
INFO 2025-07-25 00:03:14,372 autoreload 23624 16172 C:\Users\<USER>\dev\smartsupply\suptrack\admin.py changed, reloading.
INFO 2025-07-25 00:03:14,993 autoreload 3700 2276 C:\Users\<USER>\dev\smartsupply\suptrack\admin.py changed, reloading.
INFO 2025-07-25 00:03:15,058 autoreload 15404 15140 Watching for file changes with StatReloader
INFO 2025-07-25 00:03:16,182 autoreload 8268 24548 Watching for file changes with StatReloader
INFO 2025-07-25 00:05:38,962 autoreload 15404 15140 C:\Users\<USER>\dev\smartsupply\suptrack\migrations\0001_initial.py changed, reloading.
INFO 2025-07-25 00:05:39,246 autoreload 8268 24548 C:\Users\<USER>\dev\smartsupply\suptrack\migrations\0001_initial.py changed, reloading.
INFO 2025-07-25 00:05:39,904 autoreload 3916 24288 Watching for file changes with StatReloader
INFO 2025-07-25 00:05:40,183 autoreload 23176 24636 Watching for file changes with StatReloader
INFO 2025-07-25 00:06:15,711 autoreload 3916 24288 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:06:16,255 autoreload 23176 24636 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:06:16,713 autoreload 16892 11836 Watching for file changes with StatReloader
INFO 2025-07-25 00:06:17,346 autoreload 1776 16268 Watching for file changes with StatReloader
INFO 2025-07-25 00:06:33,071 autoreload 1776 16268 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:06:33,241 autoreload 16892 11836 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:06:34,235 autoreload 4480 24768 Watching for file changes with StatReloader
INFO 2025-07-25 00:06:34,250 autoreload 23316 25896 Watching for file changes with StatReloader
INFO 2025-07-25 00:06:39,649 autoreload 23316 25896 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:06:39,726 autoreload 4480 24768 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:06:40,581 autoreload 5484 25436 Watching for file changes with StatReloader
INFO 2025-07-25 00:06:40,829 autoreload 24420 11944 Watching for file changes with StatReloader
INFO 2025-07-25 00:07:18,498 autoreload 5484 25436 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:07:19,217 autoreload 24420 11944 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:07:19,323 autoreload 21120 12020 Watching for file changes with StatReloader
INFO 2025-07-25 00:07:20,586 autoreload 10528 26564 Watching for file changes with StatReloader
INFO 2025-07-25 00:07:29,000 autoreload 21120 12020 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:07:29,134 autoreload 10528 26564 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:07:30,146 autoreload 10080 14456 Watching for file changes with StatReloader
INFO 2025-07-25 00:07:30,475 autoreload 11032 17740 Watching for file changes with StatReloader
INFO 2025-07-25 00:07:38,010 autoreload 11032 17740 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:07:38,681 autoreload 10080 14456 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:07:38,849 autoreload 6720 25152 Watching for file changes with StatReloader
INFO 2025-07-25 00:07:39,715 autoreload 2912 5836 Watching for file changes with StatReloader
INFO 2025-07-25 00:08:32,498 autoreload 6720 25152 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:08:32,942 autoreload 2912 5836 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:08:33,224 autoreload 11500 5132 Watching for file changes with StatReloader
INFO 2025-07-25 00:08:34,065 autoreload 24796 3024 Watching for file changes with StatReloader
INFO 2025-07-25 00:08:59,763 autoreload 24796 3024 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:09:00,177 autoreload 11500 5132 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:09:00,875 autoreload 24236 23396 Watching for file changes with StatReloader
INFO 2025-07-25 00:09:01,407 autoreload 25484 19880 Watching for file changes with StatReloader
INFO 2025-07-25 00:09:23,320 autoreload 25484 19880 C:\Users\<USER>\dev\smartsupply\suptrack\urls.py changed, reloading.
INFO 2025-07-25 00:09:23,508 autoreload 24236 23396 C:\Users\<USER>\dev\smartsupply\suptrack\urls.py changed, reloading.
INFO 2025-07-25 00:09:23,994 autoreload 7484 5900 Watching for file changes with StatReloader
INFO 2025-07-25 00:09:24,167 autoreload 24196 22168 Watching for file changes with StatReloader
INFO 2025-07-25 00:10:57,370 autoreload 24196 22168 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:10:57,895 autoreload 7484 5900 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:10:59,325 autoreload 9996 24784 Watching for file changes with StatReloader
INFO 2025-07-25 00:11:00,072 autoreload 6748 12644 Watching for file changes with StatReloader
INFO 2025-07-25 00:13:24,441 autoreload 9996 24784 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:13:25,173 autoreload 192 15692 Watching for file changes with StatReloader
INFO 2025-07-25 00:13:25,201 autoreload 6748 12644 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:13:25,863 autoreload 8996 4764 Watching for file changes with StatReloader
INFO 2025-07-25 00:14:04,064 autoreload 192 15692 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:14:04,229 autoreload 8996 4764 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:14:04,970 autoreload 21692 24880 Watching for file changes with StatReloader
INFO 2025-07-25 00:14:05,324 autoreload 12636 23228 Watching for file changes with StatReloader
INFO 2025-07-25 00:14:10,464 autoreload 21692 24880 C:\Users\<USER>\dev\smartsupply\suptrack\urls.py changed, reloading.
INFO 2025-07-25 00:14:10,863 autoreload 12636 23228 C:\Users\<USER>\dev\smartsupply\suptrack\urls.py changed, reloading.
INFO 2025-07-25 00:14:11,160 autoreload 13068 22832 Watching for file changes with StatReloader
INFO 2025-07-25 00:14:11,986 autoreload 1776 17380 Watching for file changes with StatReloader
INFO 2025-07-25 00:16:18,485 autoreload 1776 17380 C:\Users\<USER>\dev\smartsupply\suptrack\models.py changed, reloading.
INFO 2025-07-25 00:16:19,256 autoreload 13068 22832 C:\Users\<USER>\dev\smartsupply\suptrack\models.py changed, reloading.
INFO 2025-07-25 00:16:19,967 autoreload 24016 23216 Watching for file changes with StatReloader
INFO 2025-07-25 00:16:20,559 autoreload 12780 17404 Watching for file changes with StatReloader
INFO 2025-07-25 00:16:42,921 autoreload 24016 23216 C:\Users\<USER>\dev\smartsupply\suptrack\admin.py changed, reloading.
INFO 2025-07-25 00:16:43,117 autoreload 12780 17404 C:\Users\<USER>\dev\smartsupply\suptrack\admin.py changed, reloading.
INFO 2025-07-25 00:16:43,697 autoreload 25092 24728 Watching for file changes with StatReloader
INFO 2025-07-25 00:16:43,918 autoreload 23144 18952 Watching for file changes with StatReloader
INFO 2025-07-25 00:16:56,409 autoreload 25092 24728 C:\Users\<USER>\dev\smartsupply\suptrack\admin.py changed, reloading.
INFO 2025-07-25 00:16:56,551 autoreload 23144 18952 C:\Users\<USER>\dev\smartsupply\suptrack\admin.py changed, reloading.
INFO 2025-07-25 00:16:58,087 autoreload 12548 5444 Watching for file changes with StatReloader
INFO 2025-07-25 00:16:58,348 autoreload 15892 7764 Watching for file changes with StatReloader
INFO 2025-07-25 00:19:51,442 autoreload 15892 7764 C:\Users\<USER>\dev\smartsupply\suptrack\models.py changed, reloading.
INFO 2025-07-25 00:19:51,730 autoreload 12548 5444 C:\Users\<USER>\dev\smartsupply\suptrack\models.py changed, reloading.
INFO 2025-07-25 00:19:52,995 autoreload 7392 2520 Watching for file changes with StatReloader
INFO 2025-07-25 00:19:53,214 autoreload 23228 16228 Watching for file changes with StatReloader
INFO 2025-07-25 00:20:35,622 autoreload 7392 2520 C:\Users\<USER>\dev\smartsupply\suptrack\apps.py changed, reloading.
INFO 2025-07-25 00:20:36,291 autoreload 23228 16228 C:\Users\<USER>\dev\smartsupply\suptrack\apps.py changed, reloading.
INFO 2025-07-25 00:20:36,470 autoreload 10080 23616 Watching for file changes with StatReloader
INFO 2025-07-25 00:20:37,321 autoreload 5280 25648 Watching for file changes with StatReloader
INFO 2025-07-25 00:20:46,741 autoreload 5280 25648 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:20:47,046 autoreload 10080 23616 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:20:48,012 autoreload 15992 21136 Watching for file changes with StatReloader
INFO 2025-07-25 00:20:48,543 autoreload 11252 25088 Watching for file changes with StatReloader
INFO 2025-07-25 00:21:03,154 autoreload 11252 25088 C:\Users\<USER>\dev\smartsupply\smartsupply\settings.py changed, reloading.
INFO 2025-07-25 00:21:03,577 autoreload 15992 21136 C:\Users\<USER>\dev\smartsupply\smartsupply\settings.py changed, reloading.
INFO 2025-07-25 00:21:04,232 autoreload 15176 23896 Watching for file changes with StatReloader
INFO 2025-07-25 00:21:04,778 autoreload 7920 5852 Watching for file changes with StatReloader
INFO 2025-07-25 00:21:29,299 autoreload 15176 23896 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:21:29,507 autoreload 7920 5852 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:21:30,446 autoreload 20996 10768 Watching for file changes with StatReloader
INFO 2025-07-25 00:21:30,530 autoreload 21240 19044 Watching for file changes with StatReloader
INFO 2025-07-25 00:21:43,917 autoreload 21240 19044 C:\Users\<USER>\dev\smartsupply\suptrack\urls.py changed, reloading.
INFO 2025-07-25 00:21:44,043 autoreload 20996 10768 C:\Users\<USER>\dev\smartsupply\suptrack\urls.py changed, reloading.
INFO 2025-07-25 00:21:44,893 autoreload 15996 25096 Watching for file changes with StatReloader
INFO 2025-07-25 00:21:45,146 autoreload 26064 15772 Watching for file changes with StatReloader
INFO 2025-07-25 00:22:04,585 autoreload 15996 25096 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:22:04,958 autoreload 26064 15772 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:22:05,742 autoreload 13872 16376 Watching for file changes with StatReloader
INFO 2025-07-25 00:22:06,305 autoreload 16216 24348 Watching for file changes with StatReloader
INFO 2025-07-25 00:22:12,773 autoreload 16216 24348 C:\Users\<USER>\dev\smartsupply\suptrack\urls.py changed, reloading.
INFO 2025-07-25 00:22:13,214 autoreload 13872 16376 C:\Users\<USER>\dev\smartsupply\suptrack\urls.py changed, reloading.
INFO 2025-07-25 00:22:13,803 autoreload 19128 14116 Watching for file changes with StatReloader
INFO 2025-07-25 00:22:14,200 autoreload 3960 13232 Watching for file changes with StatReloader
INFO 2025-07-25 00:22:25,407 autoreload 19128 14116 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:22:25,604 autoreload 3960 13232 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:22:26,480 autoreload 24252 25828 Watching for file changes with StatReloader
INFO 2025-07-25 00:22:26,588 autoreload 12724 7840 Watching for file changes with StatReloader
INFO 2025-07-25 00:22:31,980 autoreload 24252 25828 C:\Users\<USER>\dev\smartsupply\suptrack\urls.py changed, reloading.
INFO 2025-07-25 00:22:32,064 autoreload 12724 7840 C:\Users\<USER>\dev\smartsupply\suptrack\urls.py changed, reloading.
INFO 2025-07-25 00:22:32,714 autoreload 10076 24152 Watching for file changes with StatReloader
INFO 2025-07-25 00:22:32,782 autoreload 22016 6752 Watching for file changes with StatReloader
INFO 2025-07-25 00:23:08,485 autoreload 10076 24152 C:\Users\<USER>\dev\smartsupply\suptrack\forms.py changed, reloading.
INFO 2025-07-25 00:23:08,839 autoreload 22016 6752 C:\Users\<USER>\dev\smartsupply\suptrack\forms.py changed, reloading.
INFO 2025-07-25 00:23:09,289 autoreload 22596 15612 Watching for file changes with StatReloader
INFO 2025-07-25 00:23:10,166 autoreload 23296 25968 Watching for file changes with StatReloader
INFO 2025-07-25 00:23:24,801 autoreload 23296 25968 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:23:25,027 autoreload 22596 15612 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:23:25,873 autoreload 17608 10444 Watching for file changes with StatReloader
INFO 2025-07-25 00:23:26,129 autoreload 25100 23768 Watching for file changes with StatReloader
INFO 2025-07-25 00:23:34,804 autoreload 25100 23768 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:23:35,533 autoreload 8468 7372 Watching for file changes with StatReloader
INFO 2025-07-25 00:23:35,633 autoreload 17608 10444 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:23:36,642 autoreload 18912 16848 Watching for file changes with StatReloader
INFO 2025-07-25 00:23:56,278 autoreload 8468 7372 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:23:56,554 autoreload 18912 16848 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:23:57,334 autoreload 22868 22960 Watching for file changes with StatReloader
INFO 2025-07-25 00:23:57,692 autoreload 13524 3212 Watching for file changes with StatReloader
INFO 2025-07-25 00:24:01,688 autoreload 22868 22960 C:\Users\<USER>\dev\smartsupply\suptrack\urls.py changed, reloading.
INFO 2025-07-25 00:24:02,041 autoreload 13524 3212 C:\Users\<USER>\dev\smartsupply\suptrack\urls.py changed, reloading.
INFO 2025-07-25 00:24:02,339 autoreload 20076 23232 Watching for file changes with StatReloader
INFO 2025-07-25 00:24:03,096 autoreload 1868 8772 Watching for file changes with StatReloader
INFO 2025-07-25 00:24:48,371 autoreload 20076 23232 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:24:48,602 autoreload 1868 8772 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:24:49,576 autoreload 9848 16204 Watching for file changes with StatReloader
INFO 2025-07-25 00:24:50,022 autoreload 24428 7096 Watching for file changes with StatReloader
INFO 2025-07-25 00:25:13,933 autoreload 24428 7096 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:25:14,302 autoreload 9848 16204 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:25:15,467 autoreload 7064 15140 Watching for file changes with StatReloader
INFO 2025-07-25 00:25:15,847 autoreload 2108 18680 Watching for file changes with StatReloader
INFO 2025-07-25 00:25:20,180 autoreload 2108 18680 C:\Users\<USER>\dev\smartsupply\suptrack\urls.py changed, reloading.
INFO 2025-07-25 00:25:20,753 autoreload 17668 2988 Watching for file changes with StatReloader
INFO 2025-07-25 00:25:20,965 autoreload 7064 15140 C:\Users\<USER>\dev\smartsupply\suptrack\urls.py changed, reloading.
INFO 2025-07-25 00:25:22,029 autoreload 23148 23124 Watching for file changes with StatReloader
INFO 2025-07-25 02:12:24,110 autoreload 26788 26792 Watching for file changes with StatReloader
INFO 2025-07-25 02:12:27,286 basehttp 17668 26976 "GET / HTTP/1.1" 302 0
INFO 2025-07-25 02:12:27,557 basehttp 17668 26976 "GET /login/?next=/ HTTP/1.1" 200 13742
INFO 2025-07-25 02:12:27,673 basehttp 17668 26976 "GET /static/css/dashboard.css HTTP/1.1" 304 0
INFO 2025-07-25 02:12:27,674 basehttp 17668 23660 "GET /static/js/dashboard.js HTTP/1.1" 304 0
INFO 2025-07-25 02:12:36,660 basehttp 17668 23660 "POST /login/?next=/ HTTP/1.1" 302 0
INFO 2025-07-25 02:12:36,672 middleware 17668 23660 New session started for user: lanzy from IP: 127.0.0.1
ERROR 2025-07-25 02:12:36,677 middleware 17668 23660 Exception for user lanzy: FieldError: Cannot resolve keyword 'request_date' into field. Choices are: approved_by, approved_by_id, approved_date, department, id, justification, notes, rejection_reason, request_id, request_items, requested_date, requester, requester_id, required_date, status
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\views.py", line 70, in dashboard
    user_requests = SupplyRequest.objects.filter(requester=request.user).order_by('-request_date')
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\models\query.py", line 1722, in order_by
    obj.query.add_ordering(*field_names)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\models\sql\query.py", line 2291, in add_ordering
    self.names_to_path(item.split(LOOKUP_SEP), self.model._meta)
    ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\models\sql\query.py", line 1805, in names_to_path
    raise FieldError(
    ...<2 lines>...
    )
django.core.exceptions.FieldError: Cannot resolve keyword 'request_date' into field. Choices are: approved_by, approved_by_id, approved_date, department, id, justification, notes, rejection_reason, request_id, request_items, requested_date, requester, requester_id, required_date, status
ERROR 2025-07-25 02:12:36,984 log 17668 23660 Internal Server Error: /
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\views.py", line 70, in dashboard
    user_requests = SupplyRequest.objects.filter(requester=request.user).order_by('-request_date')
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\models\query.py", line 1722, in order_by
    obj.query.add_ordering(*field_names)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\models\sql\query.py", line 2291, in add_ordering
    self.names_to_path(item.split(LOOKUP_SEP), self.model._meta)
    ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\models\sql\query.py", line 1805, in names_to_path
    raise FieldError(
    ...<2 lines>...
    )
django.core.exceptions.FieldError: Cannot resolve keyword 'request_date' into field. Choices are: approved_by, approved_by_id, approved_date, department, id, justification, notes, rejection_reason, request_id, request_items, requested_date, requester, requester_id, required_date, status
ERROR 2025-07-25 02:12:36,991 basehttp 17668 23660 "GET / HTTP/1.1" 500 91959
INFO 2025-07-25 02:20:34,667 middleware 17668 10572 New session started for user: lanzy from IP: 127.0.0.1
ERROR 2025-07-25 02:20:34,674 middleware 17668 10572 Exception for user lanzy: NoReverseMatch: Reverse for 'password_reset' not found. 'password_reset' is not a valid view function or pattern name.
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\views.py", line 49, in login_view
    return render(request, 'registration/login.html', {'form': form})
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'password_reset' not found. 'password_reset' is not a valid view function or pattern name.
ERROR 2025-07-25 02:20:34,943 log 17668 10572 Internal Server Error: /login/
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\views.py", line 49, in login_view
    return render(request, 'registration/login.html', {'form': form})
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'password_reset' not found. 'password_reset' is not a valid view function or pattern name.
ERROR 2025-07-25 02:20:34,946 basehttp 17668 10572 "GET /login/?next=/ HTTP/1.1" 500 197890
INFO 2025-07-25 02:21:26,035 autoreload 26788 26792 C:\Users\<USER>\dev\smartsupply\suptrack\urls.py changed, reloading.
INFO 2025-07-25 02:21:26,058 autoreload 23148 23124 C:\Users\<USER>\dev\smartsupply\suptrack\urls.py changed, reloading.
INFO 2025-07-25 02:21:26,311 autoreload 17668 2988 C:\Users\<USER>\dev\smartsupply\suptrack\urls.py changed, reloading.
INFO 2025-07-25 02:21:26,943 autoreload 6796 15104 Watching for file changes with StatReloader
INFO 2025-07-25 02:21:27,192 autoreload 28416 26700 Watching for file changes with StatReloader
INFO 2025-07-25 02:21:27,685 autoreload 2752 25348 Watching for file changes with StatReloader
INFO 2025-07-25 02:21:45,415 autoreload 2752 25348 C:\Users\<USER>\dev\smartsupply\suptrack\urls.py changed, reloading.
INFO 2025-07-25 02:21:45,419 autoreload 6796 15104 C:\Users\<USER>\dev\smartsupply\suptrack\urls.py changed, reloading.
INFO 2025-07-25 02:21:45,603 autoreload 28416 26700 C:\Users\<USER>\dev\smartsupply\suptrack\urls.py changed, reloading.
INFO 2025-07-25 02:21:46,293 autoreload 15120 9704 Watching for file changes with StatReloader
INFO 2025-07-25 02:21:46,420 autoreload 13648 7208 Watching for file changes with StatReloader
INFO 2025-07-25 02:21:46,460 autoreload 6088 20000 Watching for file changes with StatReloader
INFO 2025-07-25 02:22:52,431 middleware 15120 26556 New session started for user: lanzy from IP: 127.0.0.1
ERROR 2025-07-25 02:22:52,474 middleware 15120 26556 Exception for user lanzy: NoReverseMatch: Reverse for 'password_reset' not found. 'password_reset' is not a valid view function or pattern name.
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\views.py", line 49, in login_view
    return render(request, 'registration/login.html', {'form': form})
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'password_reset' not found. 'password_reset' is not a valid view function or pattern name.
ERROR 2025-07-25 02:22:52,521 log 15120 26556 Internal Server Error: /login/
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\views.py", line 49, in login_view
    return render(request, 'registration/login.html', {'form': form})
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'password_reset' not found. 'password_reset' is not a valid view function or pattern name.
ERROR 2025-07-25 02:22:52,524 basehttp 15120 26556 "GET /login/?next=/ HTTP/1.1" 500 197795
INFO 2025-07-25 02:22:54,538 middleware 15120 26556 New session started for user: lanzy from IP: 127.0.0.1
ERROR 2025-07-25 02:22:54,541 middleware 15120 26556 Exception for user lanzy: NoReverseMatch: Reverse for 'password_reset' not found. 'password_reset' is not a valid view function or pattern name.
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\views.py", line 49, in login_view
    return render(request, 'registration/login.html', {'form': form})
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'password_reset' not found. 'password_reset' is not a valid view function or pattern name.
ERROR 2025-07-25 02:22:54,574 log 15120 26556 Internal Server Error: /login/
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\views.py", line 49, in login_view
    return render(request, 'registration/login.html', {'form': form})
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'password_reset' not found. 'password_reset' is not a valid view function or pattern name.
ERROR 2025-07-25 02:22:54,578 basehttp 15120 26556 "GET /login/?next=/ HTTP/1.1" 500 197795
INFO 2025-07-25 02:23:35,064 autoreload 15784 6608 Watching for file changes with StatReloader
INFO 2025-07-25 02:23:56,123 basehttp 15120 20204 "GET / HTTP/1.1" 302 0
ERROR 2025-07-25 02:23:56,142 middleware 15120 20204 Exception for user : NoReverseMatch: Reverse for 'password_reset' not found. 'password_reset' is not a valid view function or pattern name.
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\views.py", line 49, in login_view
    return render(request, 'registration/login.html', {'form': form})
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'password_reset' not found. 'password_reset' is not a valid view function or pattern name.
ERROR 2025-07-25 02:23:56,218 log 15120 20204 Internal Server Error: /login/
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\views.py", line 49, in login_view
    return render(request, 'registration/login.html', {'form': form})
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'password_reset' not found. 'password_reset' is not a valid view function or pattern name.
ERROR 2025-07-25 02:23:56,222 basehttp 15120 20204 "GET /login/?next=/ HTTP/1.1" 500 197390
WARNING 2025-07-25 02:23:56,484 log 15120 20204 Not Found: /favicon.ico
WARNING 2025-07-25 02:23:56,485 basehttp 15120 20204 "GET /favicon.ico HTTP/1.1" 404 8721
INFO 2025-07-25 02:24:57,637 basehttp 15120 9012 "GET /login/?next=/ HTTP/1.1" 200 14412
INFO 2025-07-25 02:24:57,665 basehttp 15120 9012 "GET /static/css/dashboard.css HTTP/1.1" 304 0
INFO 2025-07-25 02:24:57,666 basehttp 15120 27752 "GET /static/js/dashboard.js HTTP/1.1" 304 0
INFO 2025-07-25 02:25:09,537 autoreload 5600 14728 Watching for file changes with StatReloader
INFO 2025-07-25 02:27:04,032 autoreload 428 21756 Watching for file changes with StatReloader
INFO 2025-07-25 02:27:08,378 basehttp 13648 24232 "GET /login/?next=/ HTTP/1.1" 200 13552
INFO 2025-07-25 02:27:22,817 basehttp 13648 24232 "POST /login/?next=/ HTTP/1.1" 302 0
INFO 2025-07-25 02:27:22,823 middleware 13648 24232 New session started for user: lanzy from IP: 127.0.0.1
ERROR 2025-07-25 02:27:22,825 middleware 13648 24232 Exception for user lanzy: FieldError: Cannot resolve keyword 'request_date' into field. Choices are: approved_by, approved_by_id, approved_date, department, id, justification, notes, rejection_reason, request_id, request_items, requested_date, requester, requester_id, required_date, status
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\views.py", line 70, in dashboard
    user_requests = SupplyRequest.objects.filter(requester=request.user).order_by('-request_date')
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\models\query.py", line 1722, in order_by
    obj.query.add_ordering(*field_names)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\models\sql\query.py", line 2291, in add_ordering
    self.names_to_path(item.split(LOOKUP_SEP), self.model._meta)
    ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\models\sql\query.py", line 1805, in names_to_path
    raise FieldError(
    ...<2 lines>...
    )
django.core.exceptions.FieldError: Cannot resolve keyword 'request_date' into field. Choices are: approved_by, approved_by_id, approved_date, department, id, justification, notes, rejection_reason, request_id, request_items, requested_date, requester, requester_id, required_date, status
ERROR 2025-07-25 02:27:22,865 log 13648 24232 Internal Server Error: /
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\views.py", line 70, in dashboard
    user_requests = SupplyRequest.objects.filter(requester=request.user).order_by('-request_date')
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\models\query.py", line 1722, in order_by
    obj.query.add_ordering(*field_names)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\models\sql\query.py", line 2291, in add_ordering
    self.names_to_path(item.split(LOOKUP_SEP), self.model._meta)
    ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\models\sql\query.py", line 1805, in names_to_path
    raise FieldError(
    ...<2 lines>...
    )
django.core.exceptions.FieldError: Cannot resolve keyword 'request_date' into field. Choices are: approved_by, approved_by_id, approved_date, department, id, justification, notes, rejection_reason, request_id, request_items, requested_date, requester, requester_id, required_date, status
ERROR 2025-07-25 02:27:22,867 basehttp 13648 24232 "GET / HTTP/1.1" 500 91839
INFO 2025-07-25 02:27:58,615 autoreload 6088 20000 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 02:27:59,102 autoreload 428 21756 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 02:27:59,427 autoreload 13648 7208 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 02:27:59,746 autoreload 27740 27628 Watching for file changes with StatReloader
INFO 2025-07-25 02:28:00,054 autoreload 25376 27004 Watching for file changes with StatReloader
INFO 2025-07-25 02:28:00,551 autoreload 24184 27188 Watching for file changes with StatReloader
INFO 2025-07-25 02:28:17,849 autoreload 24184 27188 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 02:28:18,363 autoreload 27740 27628 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 02:28:18,440 autoreload 28308 24676 Watching for file changes with StatReloader
INFO 2025-07-25 02:28:18,596 autoreload 25376 27004 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 02:28:19,292 autoreload 13612 24996 Watching for file changes with StatReloader
INFO 2025-07-25 02:28:19,349 autoreload 28100 28268 Watching for file changes with StatReloader
INFO 2025-07-25 02:28:33,007 autoreload 15252 200 Watching for file changes with StatReloader
INFO 2025-07-25 02:28:38,208 middleware 28308 14900 New session started for user: lanzy from IP: 127.0.0.1
INFO 2025-07-25 02:28:38,237 basehttp 28308 14900 "GET / HTTP/1.1" 200 12688
INFO 2025-07-25 02:28:48,006 basehttp 28308 8096 "GET /supply-request/new/ HTTP/1.1" 200 16434
INFO 2025-07-25 02:28:50,638 basehttp 28308 8096 "GET /supply-request/ HTTP/1.1" 200 13287
INFO 2025-07-25 02:30:38,031 basehttp 28308 23152 "GET /supply-request/ HTTP/1.1" 200 13810
INFO 2025-07-25 02:36:02,674 basehttp 28308 27084 "GET /supply-request/ HTTP/1.1" 200 13287
INFO 2025-07-25 02:36:55,173 basehttp 28308 27084 "GET /supply-request/ HTTP/1.1" 200 13287
INFO 2025-07-25 02:37:33,492 middleware 28308 26724 New session started for user: lanzy from IP: 127.0.0.1
INFO 2025-07-25 02:37:33,504 basehttp 28308 26724 "GET /login/?next=/ HTTP/1.1" 200 14668
INFO 2025-07-25 02:37:37,502 basehttp 28308 26724 "GET /register/ HTTP/1.1" 200 16547
INFO 2025-07-25 02:37:37,549 basehttp 28308 26724 "GET /static/css/dashboard.css HTTP/1.1" 304 0
INFO 2025-07-25 02:37:37,551 basehttp 28308 3016 "GET /static/js/dashboard.js HTTP/1.1" 304 0
INFO 2025-07-25 02:37:40,752 basehttp 28308 3016 "GET /login/ HTTP/1.1" 200 14668
INFO 2025-07-25 02:37:45,946 basehttp 28308 3016 "POST /login/ HTTP/1.1" 302 0
INFO 2025-07-25 02:37:45,981 basehttp 28308 3016 "GET / HTTP/1.1" 200 12688
INFO 2025-07-25 02:37:53,210 basehttp 28308 3016 "GET /supply-request/new/ HTTP/1.1" 200 16434
INFO 2025-07-25 12:41:37,046 autoreload 26248 4564 Watching for file changes with StatReloader
INFO 2025-07-25 12:41:39,683 basehttp 26248 8420 "GET / HTTP/1.1" 302 0
INFO 2025-07-25 12:41:39,707 basehttp 26248 8420 "GET /login/?next=/ HTTP/1.1" 200 13552
INFO 2025-07-25 12:41:39,819 basehttp 26248 24040 "GET /static/js/dashboard.js HTTP/1.1" 304 0
INFO 2025-07-25 12:41:39,819 basehttp 26248 8420 "GET /static/css/dashboard.css HTTP/1.1" 304 0
WARNING 2025-07-25 12:41:40,556 log 26248 8420 Not Found: /favicon.ico
WARNING 2025-07-25 12:41:40,557 basehttp 26248 8420 "GET /favicon.ico HTTP/1.1" 404 8721
INFO 2025-07-25 12:41:46,997 basehttp 26248 8420 "POST /login/?next=/ HTTP/1.1" 302 0
INFO 2025-07-25 12:41:47,014 middleware 26248 8420 New session started for user: lanzy from IP: 127.0.0.1
INFO 2025-07-25 12:41:47,055 basehttp 26248 8420 "GET / HTTP/1.1" 200 12688
INFO 2025-07-25 12:41:49,666 basehttp 26248 8420 "GET /supply-request/new/ HTTP/1.1" 200 16434
INFO 2025-07-25 12:41:51,611 basehttp 26248 22068 "GET /supply-request/ HTTP/1.1" 200 13287
INFO 2025-07-25 12:41:53,085 basehttp 26248 22068 "GET /logout/ HTTP/1.1" 302 0
INFO 2025-07-25 12:41:53,094 basehttp 26248 22068 "GET /login/ HTTP/1.1" 200 13552
INFO 2025-07-25 12:41:57,395 basehttp 26248 22068 "GET /register/ HTTP/1.1" 200 15431
INFO 2025-07-25 12:42:29,940 basehttp 26248 28388 "POST /register/ HTTP/1.1" 200 15579
INFO 2025-07-25 12:42:52,611 basehttp 26248 12128 "GET /login/ HTTP/1.1" 200 13552
INFO 2025-07-25 12:46:36,334 basehttp 26248 6884 "POST /login/ HTTP/1.1" 302 0
INFO 2025-07-25 12:46:36,347 middleware 26248 6884 New session started for user: lanzy from IP: 127.0.0.1
INFO 2025-07-25 12:46:36,380 basehttp 26248 6884 "GET / HTTP/1.1" 200 12688
INFO 2025-07-25 12:46:39,886 basehttp 26248 6884 "GET / HTTP/1.1" 200 12688
INFO 2025-07-25 12:46:41,023 basehttp 26248 6884 "GET / HTTP/1.1" 200 12688
INFO 2025-07-25 12:46:44,549 basehttp 26248 6884 "GET /supply-request/new/ HTTP/1.1" 200 16434
INFO 2025-07-25 12:47:22,528 autoreload 12104 7404 Watching for file changes with StatReloader
INFO 2025-07-25 12:47:24,479 basehttp 26248 27148 "GET / HTTP/1.1" 200 12688
INFO 2025-07-25 13:03:18,312 basehttp 26248 7524 "GET / HTTP/1.1" 200 24720
INFO 2025-07-25 13:03:21,488 basehttp 26248 7524 "GET /supply-request/new/ HTTP/1.1" 200 18443
INFO 2025-07-25 13:03:22,981 basehttp 26248 7524 "GET /supply-request/ HTTP/1.1" 200 15296
INFO 2025-07-25 13:03:25,588 basehttp 26248 7524 "GET / HTTP/1.1" 200 24720
INFO 2025-07-25 13:03:28,197 basehttp 26248 7524 "GET /supply-request/new/ HTTP/1.1" 200 18443
INFO 2025-07-25 13:03:30,213 basehttp 26248 7524 "GET /supply-request/new/ HTTP/1.1" 200 18443
INFO 2025-07-25 13:03:31,190 basehttp 26248 7524 "GET /supply-request/ HTTP/1.1" 200 15296
INFO 2025-07-25 13:03:35,321 basehttp 26248 7524 "GET /supply-request/new/ HTTP/1.1" 200 18443
INFO 2025-07-25 13:26:59,798 basehttp 26248 11144 "GET /supply-request/new/ HTTP/1.1" 200 36525
INFO 2025-07-25 13:27:12,074 basehttp 26248 11144 "GET /supply-request/ HTTP/1.1" 200 29249
INFO 2025-07-25 13:27:14,456 basehttp 26248 11144 "GET /supply-request/new/ HTTP/1.1" 200 36525
INFO 2025-07-25 13:29:39,047 autoreload 26248 4564 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 13:29:39,194 autoreload 12104 7404 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 13:29:41,241 autoreload 28416 1896 Watching for file changes with StatReloader
INFO 2025-07-25 13:29:41,241 autoreload 20108 21316 Watching for file changes with StatReloader
ERROR 2025-07-25 13:48:52,433 manage_users 5628 25376 Command failed: User 'admin' already exists
ERROR 2025-07-25 13:48:53,190 manage_users 22600 8176 Command failed: File 'users.csv' does not exist
ERROR 2025-07-25 13:54:23,130 manage_inventory 26512 7804 Command failed: '<=' not supported between instances of 'int' and 'str'
INFO 2025-07-25 13:54:45,760 autoreload 8848 9500 Watching for file changes with StatReloader
INFO 2025-07-25 13:54:49,578 basehttp 28416 9388 "GET /supply-request/new/ HTTP/1.1" 200 36948
INFO 2025-07-25 13:54:53,678 basehttp 28416 9388 "GET /supply-request/ HTTP/1.1" 200 29249
INFO 2025-07-25 13:54:56,963 basehttp 28416 9388 "GET /supply-request/new/ HTTP/1.1" 200 36948
ERROR 2025-07-25 13:54:58,282 manage_inventory 8084 24864 Command failed: '<=' not supported between instances of 'int' and 'str'
INFO 2025-07-25 13:55:33,660 basehttp 28416 21028 "POST /supply-request/new/ HTTP/1.1" 302 0
INFO 2025-07-25 13:55:33,703 basehttp 28416 21028 "GET /supply-request/10/ HTTP/1.1" 200 24814
INFO 2025-07-25 13:55:44,339 basehttp 28416 25408 "GET /supply-request/ HTTP/1.1" 200 36505
INFO 2025-07-25 13:55:47,814 basehttp 28416 25408 "GET /supply-request/10/ HTTP/1.1" 200 24814
INFO 2025-07-25 13:55:49,556 basehttp 28416 25408 "GET /supply-request/ HTTP/1.1" 200 36505
INFO 2025-07-25 13:55:56,678 basehttp 28416 27056 "GET /logout/ HTTP/1.1" 302 0
ERROR 2025-07-25 13:55:56,692 middleware 28416 27056 Exception for user : VariableDoesNotExist: Failed lookup for key [0] in ''
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 891, in _resolve_lookup
    current = current[bit]
              ~~~~~~~^^^^^
TypeError: string indices must be integers, not 'str'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 901, in _resolve_lookup
    current = getattr(current, bit)
AttributeError: 'str' object has no attribute '0'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 907, in _resolve_lookup
    current = current[int(bit)]
              ~~~~~~~^^^^^^^^^^
IndexError: string index out of range

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\views.py", line 49, in login_view
    return render(request, 'registration/login.html', {'form': form})
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1075, in render
    output = self.filter_expression.resolve(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 743, in resolve
    arg_vals.append(arg.resolve(context))
                    ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 854, in resolve
    value = self._resolve_lookup(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 914, in _resolve_lookup
    raise VariableDoesNotExist(
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
    )  # missing attribute
    ^
django.template.base.VariableDoesNotExist: Failed lookup for key [0] in ''
ERROR 2025-07-25 13:55:56,751 log 28416 27056 Internal Server Error: /login/
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 891, in _resolve_lookup
    current = current[bit]
              ~~~~~~~^^^^^
TypeError: string indices must be integers, not 'str'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 901, in _resolve_lookup
    current = getattr(current, bit)
AttributeError: 'str' object has no attribute '0'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 907, in _resolve_lookup
    current = current[int(bit)]
              ~~~~~~~^^^^^^^^^^
IndexError: string index out of range

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\views.py", line 49, in login_view
    return render(request, 'registration/login.html', {'form': form})
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1075, in render
    output = self.filter_expression.resolve(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 743, in resolve
    arg_vals.append(arg.resolve(context))
                    ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 854, in resolve
    value = self._resolve_lookup(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 914, in _resolve_lookup
    raise VariableDoesNotExist(
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
    )  # missing attribute
    ^
django.template.base.VariableDoesNotExist: Failed lookup for key [0] in ''
ERROR 2025-07-25 13:55:56,756 basehttp 28416 27056 "GET /login/ HTTP/1.1" 500 185110
INFO 2025-07-25 13:55:58,847 basehttp 28416 27056 "GET /supply-request/ HTTP/1.1" 302 0
ERROR 2025-07-25 13:55:58,852 middleware 28416 27056 Exception for user : VariableDoesNotExist: Failed lookup for key [0] in ''
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 891, in _resolve_lookup
    current = current[bit]
              ~~~~~~~^^^^^
TypeError: string indices must be integers, not 'str'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 901, in _resolve_lookup
    current = getattr(current, bit)
AttributeError: 'str' object has no attribute '0'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 907, in _resolve_lookup
    current = current[int(bit)]
              ~~~~~~~^^^^^^^^^^
IndexError: string index out of range

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\views.py", line 49, in login_view
    return render(request, 'registration/login.html', {'form': form})
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1075, in render
    output = self.filter_expression.resolve(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 743, in resolve
    arg_vals.append(arg.resolve(context))
                    ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 854, in resolve
    value = self._resolve_lookup(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 914, in _resolve_lookup
    raise VariableDoesNotExist(
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
    )  # missing attribute
    ^
django.template.base.VariableDoesNotExist: Failed lookup for key [0] in ''
ERROR 2025-07-25 13:55:58,901 log 28416 27056 Internal Server Error: /login/
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 891, in _resolve_lookup
    current = current[bit]
              ~~~~~~~^^^^^
TypeError: string indices must be integers, not 'str'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 901, in _resolve_lookup
    current = getattr(current, bit)
AttributeError: 'str' object has no attribute '0'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 907, in _resolve_lookup
    current = current[int(bit)]
              ~~~~~~~^^^^^^^^^^
IndexError: string index out of range

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\views.py", line 49, in login_view
    return render(request, 'registration/login.html', {'form': form})
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1075, in render
    output = self.filter_expression.resolve(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 743, in resolve
    arg_vals.append(arg.resolve(context))
                    ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 854, in resolve
    value = self._resolve_lookup(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 914, in _resolve_lookup
    raise VariableDoesNotExist(
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
    )  # missing attribute
    ^
django.template.base.VariableDoesNotExist: Failed lookup for key [0] in ''
ERROR 2025-07-25 13:55:58,908 basehttp 28416 27056 "GET /login/?next=/supply-request/ HTTP/1.1" 500 185504
INFO 2025-07-25 13:56:00,597 basehttp 28416 27056 "GET /supply-request/10/ HTTP/1.1" 302 0
ERROR 2025-07-25 13:56:00,606 middleware 28416 27056 Exception for user : VariableDoesNotExist: Failed lookup for key [0] in ''
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 891, in _resolve_lookup
    current = current[bit]
              ~~~~~~~^^^^^
TypeError: string indices must be integers, not 'str'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 901, in _resolve_lookup
    current = getattr(current, bit)
AttributeError: 'str' object has no attribute '0'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 907, in _resolve_lookup
    current = current[int(bit)]
              ~~~~~~~^^^^^^^^^^
IndexError: string index out of range

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\views.py", line 49, in login_view
    return render(request, 'registration/login.html', {'form': form})
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1075, in render
    output = self.filter_expression.resolve(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 743, in resolve
    arg_vals.append(arg.resolve(context))
                    ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 854, in resolve
    value = self._resolve_lookup(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 914, in _resolve_lookup
    raise VariableDoesNotExist(
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
    )  # missing attribute
    ^
django.template.base.VariableDoesNotExist: Failed lookup for key [0] in ''
ERROR 2025-07-25 13:56:00,656 log 28416 27056 Internal Server Error: /login/
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 891, in _resolve_lookup
    current = current[bit]
              ~~~~~~~^^^^^
TypeError: string indices must be integers, not 'str'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 901, in _resolve_lookup
    current = getattr(current, bit)
AttributeError: 'str' object has no attribute '0'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 907, in _resolve_lookup
    current = current[int(bit)]
              ~~~~~~~^^^^^^^^^^
IndexError: string index out of range

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\views.py", line 49, in login_view
    return render(request, 'registration/login.html', {'form': form})
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1075, in render
    output = self.filter_expression.resolve(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 743, in resolve
    arg_vals.append(arg.resolve(context))
                    ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 854, in resolve
    value = self._resolve_lookup(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 914, in _resolve_lookup
    raise VariableDoesNotExist(
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
    )  # missing attribute
    ^
django.template.base.VariableDoesNotExist: Failed lookup for key [0] in ''
ERROR 2025-07-25 13:56:00,660 basehttp 28416 27056 "GET /login/?next=/supply-request/10/ HTTP/1.1" 500 185531
ERROR 2025-07-25 13:56:05,227 manage_inventory 13744 24424 Command failed: '<=' not supported between instances of 'int' and 'str'
ERROR 2025-07-25 13:57:09,536 manage_inventory 25584 17096 Command failed: '<=' not supported between instances of 'int' and 'str'
ERROR 2025-07-25 13:57:55,695 manage_inventory 3744 25332 Command failed: 'SupplyItem' object has no attribute 'unit_price'
INFO 2025-07-25 13:58:29,785 manage_inventory 26832 7132 Stock adjustment: IT001 (Laptop Computer) from 25 to 20. Reason: Testing stock update
ERROR 2025-07-25 13:58:51,993 manage_users 13964 21380 Command failed: Failed to create user: NOT NULL constraint failed: user_profiles.phone_number
ERROR 2025-07-25 13:59:59,631 manage_inventory 25304 11956 Command failed: Cannot resolve keyword 'supply_request' into field. Choices are: id, notes, quantity_approved, quantity_requested, request, request_id, stocktransaction, supply_item, supply_item_id, unit_price
INFO 2025-07-25 14:05:29,474 autoreload 27164 7268 Watching for file changes with StatReloader
INFO 2025-07-25 14:05:31,863 basehttp 28416 2440 "GET / HTTP/1.1" 302 0
ERROR 2025-07-25 14:05:31,927 middleware 28416 2440 Exception for user : VariableDoesNotExist: Failed lookup for key [0] in ''
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 891, in _resolve_lookup
    current = current[bit]
              ~~~~~~~^^^^^
TypeError: string indices must be integers, not 'str'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 901, in _resolve_lookup
    current = getattr(current, bit)
AttributeError: 'str' object has no attribute '0'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 907, in _resolve_lookup
    current = current[int(bit)]
              ~~~~~~~^^^^^^^^^^
IndexError: string index out of range

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\views.py", line 49, in login_view
    return render(request, 'registration/login.html', {'form': form})
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1075, in render
    output = self.filter_expression.resolve(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 743, in resolve
    arg_vals.append(arg.resolve(context))
                    ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 854, in resolve
    value = self._resolve_lookup(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 914, in _resolve_lookup
    raise VariableDoesNotExist(
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
    )  # missing attribute
    ^
django.template.base.VariableDoesNotExist: Failed lookup for key [0] in ''
ERROR 2025-07-25 14:05:32,064 log 28416 2440 Internal Server Error: /login/
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 891, in _resolve_lookup
    current = current[bit]
              ~~~~~~~^^^^^
TypeError: string indices must be integers, not 'str'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 901, in _resolve_lookup
    current = getattr(current, bit)
AttributeError: 'str' object has no attribute '0'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 907, in _resolve_lookup
    current = current[int(bit)]
              ~~~~~~~^^^^^^^^^^
IndexError: string index out of range

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\views.py", line 49, in login_view
    return render(request, 'registration/login.html', {'form': form})
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1075, in render
    output = self.filter_expression.resolve(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 743, in resolve
    arg_vals.append(arg.resolve(context))
                    ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 854, in resolve
    value = self._resolve_lookup(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 914, in _resolve_lookup
    raise VariableDoesNotExist(
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
    )  # missing attribute
    ^
django.template.base.VariableDoesNotExist: Failed lookup for key [0] in ''
ERROR 2025-07-25 14:05:32,068 basehttp 28416 2440 "GET /login/?next=/ HTTP/1.1" 500 185316
INFO 2025-07-25 14:05:36,325 basehttp 28416 2440 "GET / HTTP/1.1" 302 0
ERROR 2025-07-25 14:05:36,331 middleware 28416 2440 Exception for user : VariableDoesNotExist: Failed lookup for key [0] in ''
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 891, in _resolve_lookup
    current = current[bit]
              ~~~~~~~^^^^^
TypeError: string indices must be integers, not 'str'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 901, in _resolve_lookup
    current = getattr(current, bit)
AttributeError: 'str' object has no attribute '0'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 907, in _resolve_lookup
    current = current[int(bit)]
              ~~~~~~~^^^^^^^^^^
IndexError: string index out of range

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\views.py", line 49, in login_view
    return render(request, 'registration/login.html', {'form': form})
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1075, in render
    output = self.filter_expression.resolve(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 743, in resolve
    arg_vals.append(arg.resolve(context))
                    ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 854, in resolve
    value = self._resolve_lookup(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 914, in _resolve_lookup
    raise VariableDoesNotExist(
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
    )  # missing attribute
    ^
django.template.base.VariableDoesNotExist: Failed lookup for key [0] in ''
ERROR 2025-07-25 14:05:36,387 log 28416 2440 Internal Server Error: /login/
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 891, in _resolve_lookup
    current = current[bit]
              ~~~~~~~^^^^^
TypeError: string indices must be integers, not 'str'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 901, in _resolve_lookup
    current = getattr(current, bit)
AttributeError: 'str' object has no attribute '0'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 907, in _resolve_lookup
    current = current[int(bit)]
              ~~~~~~~^^^^^^^^^^
IndexError: string index out of range

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\views.py", line 49, in login_view
    return render(request, 'registration/login.html', {'form': form})
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1075, in render
    output = self.filter_expression.resolve(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 743, in resolve
    arg_vals.append(arg.resolve(context))
                    ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 854, in resolve
    value = self._resolve_lookup(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 914, in _resolve_lookup
    raise VariableDoesNotExist(
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
    )  # missing attribute
    ^
django.template.base.VariableDoesNotExist: Failed lookup for key [0] in ''
ERROR 2025-07-25 14:05:36,393 basehttp 28416 2440 "GET /login/?next=/ HTTP/1.1" 500 185316
INFO 2025-07-25 14:08:32,452 autoreload 11080 8396 Watching for file changes with StatReloader
INFO 2025-07-25 14:08:34,707 basehttp 28416 14972 "GET / HTTP/1.1" 302 0
INFO 2025-07-25 14:08:34,766 basehttp 28416 14972 "GET /login/?next=/ HTTP/1.1" 200 13301
INFO 2025-07-25 14:08:45,137 basehttp 28416 14972 "POST /login/?next=/ HTTP/1.1" 200 13301
INFO 2025-07-25 14:08:48,402 basehttp 28416 14972 "GET /login/ HTTP/1.1" 200 13301
INFO 2025-07-25 14:08:59,472 basehttp 28416 7056 "POST /login/ HTTP/1.1" 200 13301
INFO 2025-07-25 14:09:50,307 basehttp 28416 14560 "POST /login/ HTTP/1.1" 200 13301
INFO 2025-07-25 14:09:50,412 basehttp 28416 14560 "GET /static/css/dashboard.css HTTP/1.1" 200 11277
INFO 2025-07-25 14:09:50,413 basehttp 28416 27976 "GET /static/js/dashboard.js HTTP/1.1" 200 12440
INFO 2025-07-25 14:11:16,240 autoreload 8848 9500 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 14:11:16,372 autoreload 28416 1896 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 14:11:16,350 autoreload 27164 7268 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 14:11:16,773 autoreload 11080 8396 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 14:11:17,780 autoreload 24880 9844 Watching for file changes with StatReloader
INFO 2025-07-25 14:11:17,780 autoreload 27652 18412 Watching for file changes with StatReloader
INFO 2025-07-25 14:11:17,780 autoreload 26512 27524 Watching for file changes with StatReloader
INFO 2025-07-25 14:11:18,025 autoreload 26724 11856 Watching for file changes with StatReloader
INFO 2025-07-25 14:11:32,370 autoreload 24880 9844 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 14:11:32,460 autoreload 27652 18412 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 14:11:32,479 autoreload 26512 27524 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 14:11:32,846 autoreload 26724 11856 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 14:11:33,277 autoreload 26320 22100 Watching for file changes with StatReloader
INFO 2025-07-25 14:11:33,481 autoreload 16280 18624 Watching for file changes with StatReloader
INFO 2025-07-25 14:11:33,511 autoreload 10312 12840 Watching for file changes with StatReloader
INFO 2025-07-25 14:11:33,946 autoreload 20280 22524 Watching for file changes with StatReloader
INFO 2025-07-25 14:11:47,688 autoreload 20280 22524 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 14:11:47,732 autoreload 26320 22100 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 14:11:47,971 autoreload 10312 12840 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 14:11:47,971 autoreload 16280 18624 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 14:11:48,767 autoreload 24652 20512 Watching for file changes with StatReloader
INFO 2025-07-25 14:11:48,968 autoreload 10108 12736 Watching for file changes with StatReloader
INFO 2025-07-25 14:11:49,170 autoreload 18272 13504 Watching for file changes with StatReloader
INFO 2025-07-25 14:11:49,177 autoreload 22816 21568 Watching for file changes with StatReloader
INFO 2025-07-25 14:14:43,890 basehttp 24652 27624 "POST /login/?next=/ HTTP/1.1" 200 13301
INFO 2025-07-25 14:14:44,118 basehttp 24652 27624 "GET /static/css/dashboard.css HTTP/1.1" 200 11277
INFO 2025-07-25 14:14:44,118 basehttp 24652 14616 "GET /static/js/dashboard.js HTTP/1.1" 200 12440
INFO 2025-07-25 14:21:16,845 autoreload 10108 12736 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 14:21:16,927 autoreload 18272 13504 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 14:21:16,940 autoreload 22816 21568 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 14:21:17,426 autoreload 24652 20512 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 14:21:17,974 autoreload 19256 28180 Watching for file changes with StatReloader
INFO 2025-07-25 14:21:17,974 autoreload 11228 23692 Watching for file changes with StatReloader
INFO 2025-07-25 14:21:18,114 autoreload 8840 16168 Watching for file changes with StatReloader
INFO 2025-07-25 14:21:18,350 autoreload 27532 27912 Watching for file changes with StatReloader
INFO 2025-07-25 14:21:25,674 autoreload 11956 9232 Watching for file changes with StatReloader
INFO 2025-07-25 14:22:14,373 basehttp 19256 5512 "GET / HTTP/1.1" 302 0
INFO 2025-07-25 14:22:14,410 basehttp 19256 5512 "GET /login/?next=/ HTTP/1.1" 200 13301
INFO 2025-07-25 14:22:14,676 basehttp 19256 5512 "GET /static/css/dashboard.css HTTP/1.1" 200 11277
INFO 2025-07-25 14:22:14,690 basehttp 19256 26144 "GET /static/js/dashboard.js HTTP/1.1" 200 12440
WARNING 2025-07-25 14:22:15,091 log 19256 26144 Not Found: /favicon.ico
WARNING 2025-07-25 14:22:15,092 basehttp 19256 26144 "GET /favicon.ico HTTP/1.1" 404 8721
INFO 2025-07-25 14:22:25,052 basehttp 19256 23980 "POST /login/?next=/ HTTP/1.1" 200 13301
INFO 2025-07-25 14:22:59,487 basehttp 19256 8668 "POST /login/?next=/ HTTP/1.1" 200 13301
INFO 2025-07-25 14:25:02,391 autoreload 2368 24940 Watching for file changes with StatReloader
INFO 2025-07-25 14:28:21,424 basehttp 2368 27624 "GET / HTTP/1.1" 302 0
INFO 2025-07-25 14:28:21,451 basehttp 2368 27624 "GET /login/?next=/ HTTP/1.1" 200 13301
INFO 2025-07-25 14:28:40,169 basehttp 2368 27624 "POST /login/?next=/ HTTP/1.1" 200 13301
INFO 2025-07-25 14:28:52,315 basehttp 2368 27624 "POST /login/?next=/ HTTP/1.1" 200 13301
INFO 2025-07-25 14:28:52,349 basehttp 2368 27624 "GET /static/css/dashboard.css HTTP/1.1" 304 0
INFO 2025-07-25 14:29:43,185 autoreload 2368 24940 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 14:29:43,912 autoreload 11480 1256 Watching for file changes with StatReloader
INFO 2025-07-25 14:30:08,775 autoreload 11480 1256 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 14:30:09,496 autoreload 13668 21096 Watching for file changes with StatReloader
INFO 2025-07-25 14:30:14,809 autoreload 13668 21096 C:\Users\<USER>\dev\smartsupply\suptrack\urls.py changed, reloading.
INFO 2025-07-25 14:30:15,426 autoreload 18068 3564 Watching for file changes with StatReloader
INFO 2025-07-25 14:31:11,033 autoreload 5296 1584 Watching for file changes with StatReloader
INFO 2025-07-25 14:31:15,757 basehttp 5296 3168 "GET /login/?next=/ HTTP/1.1" 200 13301
INFO 2025-07-25 14:31:15,773 basehttp 5296 3168 "GET /static/js/dashboard.js HTTP/1.1" 304 0
INFO 2025-07-25 14:31:21,688 basehttp 5296 3168 "GET /login/ HTTP/1.1" 200 13301
INFO 2025-07-25 14:31:32,172 basehttp 5296 7980 "POST /login/ HTTP/1.1" 200 13301
INFO 2025-07-25 14:33:05,805 autoreload 25168 26156 Watching for file changes with StatReloader
INFO 2025-07-25 14:33:28,480 basehttp 5296 1684 "GET / HTTP/1.1" 302 0
INFO 2025-07-25 14:33:28,492 basehttp 5296 1684 "GET /login/?next=/ HTTP/1.1" 200 13301
INFO 2025-07-25 14:33:42,114 basehttp 5296 14640 "POST /login/?next=/ HTTP/1.1" 200 13301
INFO 2025-07-25 14:34:37,808 autoreload 21096 13668 Watching for file changes with StatReloader
INFO 2025-07-25 14:34:42,812 basehttp 5296 3304 "GET / HTTP/1.1" 302 0
INFO 2025-07-25 14:34:42,828 basehttp 5296 3304 "GET /login/?next=/ HTTP/1.1" 200 13301
INFO 2025-07-25 14:34:43,046 basehttp 5296 3304 "GET /static/css/dashboard.css HTTP/1.1" 304 0
INFO 2025-07-25 14:34:43,046 basehttp 5296 3240 "GET /static/js/dashboard.js HTTP/1.1" 304 0
INFO 2025-07-25 14:35:00,168 basehttp 5296 18440 "POST /login/?next=/ HTTP/1.1" 302 0
INFO 2025-07-25 14:35:00,178 middleware 5296 18440 New session started for user: gsostaff from IP: 127.0.0.1
INFO 2025-07-25 14:35:00,277 basehttp 5296 18440 "GET /dashboard/gso/ HTTP/1.1" 200 70704
INFO 2025-07-25 14:35:13,294 basehttp 5296 15756 "GET /supply-request/ HTTP/1.1" 200 33540
ERROR 2025-07-25 14:35:16,241 middleware 5296 15756 Exception for user gsostaff: AttributeError: 'WSGIRequest' object has no attribute 'htmx'
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\decorators.py", line 32, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\views.py", line 235, in inventory_dashboard
    if request.htmx:
       ^^^^^^^^^^^^
AttributeError: 'WSGIRequest' object has no attribute 'htmx'
ERROR 2025-07-25 14:35:16,302 log 5296 15756 Internal Server Error: /inventory/
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\decorators.py", line 32, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\views.py", line 235, in inventory_dashboard
    if request.htmx:
       ^^^^^^^^^^^^
AttributeError: 'WSGIRequest' object has no attribute 'htmx'
ERROR 2025-07-25 14:35:16,305 basehttp 5296 15756 "GET /inventory/ HTTP/1.1" 500 82994
INFO 2025-07-25 14:35:18,459 basehttp 5296 15756 "GET /supply-request/ HTTP/1.1" 200 33540
INFO 2025-07-25 14:35:19,907 basehttp 5296 15756 "GET /inventory/qr-scanner/ HTTP/1.1" 200 19963
INFO 2025-07-25 14:35:32,567 basehttp 5296 24468 "GET /inventory/low-stock-alerts/ HTTP/1.1" 200 20337
ERROR 2025-07-25 14:35:38,021 middleware 5296 24468 Exception for user gsostaff: AttributeError: 'WSGIRequest' object has no attribute 'htmx'
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\decorators.py", line 32, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\views.py", line 235, in inventory_dashboard
    if request.htmx:
       ^^^^^^^^^^^^
AttributeError: 'WSGIRequest' object has no attribute 'htmx'
ERROR 2025-07-25 14:35:38,067 log 5296 24468 Internal Server Error: /inventory/
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\decorators.py", line 32, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\views.py", line 235, in inventory_dashboard
    if request.htmx:
       ^^^^^^^^^^^^
AttributeError: 'WSGIRequest' object has no attribute 'htmx'
ERROR 2025-07-25 14:35:38,071 basehttp 5296 24468 "GET /inventory/ HTTP/1.1" 500 83006
INFO 2025-07-25 14:36:03,085 autoreload 21096 13668 C:\Users\<USER>\dev\smartsupply\smartsupply\settings.py changed, reloading.
INFO 2025-07-25 14:36:03,140 autoreload 5296 1584 C:\Users\<USER>\dev\smartsupply\smartsupply\settings.py changed, reloading.
INFO 2025-07-25 14:36:03,825 autoreload 25168 26156 C:\Users\<USER>\dev\smartsupply\smartsupply\settings.py changed, reloading.
INFO 2025-07-25 14:36:03,911 autoreload 28056 22988 Watching for file changes with StatReloader
INFO 2025-07-25 14:36:03,994 autoreload 23048 26716 Watching for file changes with StatReloader
INFO 2025-07-25 14:36:04,585 autoreload 12724 25292 Watching for file changes with StatReloader
INFO 2025-07-25 14:36:08,883 autoreload 12724 25292 C:\Users\<USER>\dev\smartsupply\smartsupply\settings.py changed, reloading.
INFO 2025-07-25 14:36:09,173 autoreload 28056 22988 C:\Users\<USER>\dev\smartsupply\smartsupply\settings.py changed, reloading.
INFO 2025-07-25 14:36:09,394 autoreload 23048 26716 C:\Users\<USER>\dev\smartsupply\smartsupply\settings.py changed, reloading.
INFO 2025-07-25 14:36:09,819 autoreload 11808 13892 Watching for file changes with StatReloader
INFO 2025-07-25 14:36:09,839 autoreload 11080 13040 Watching for file changes with StatReloader
INFO 2025-07-25 14:36:10,326 autoreload 9260 16164 Watching for file changes with StatReloader
INFO 2025-07-25 14:36:58,499 autoreload 15264 3016 Watching for file changes with StatReloader
INFO 2025-07-25 14:37:12,074 basehttp 11808 7488 "POST /login/?next=/ HTTP/1.1" 302 0
INFO 2025-07-25 14:37:12,089 middleware 11808 7488 New session started for user: gsostaff from IP: 127.0.0.1
INFO 2025-07-25 14:37:12,225 basehttp 11808 7488 "GET /dashboard/gso/ HTTP/1.1" 200 70704
INFO 2025-07-25 14:37:12,262 basehttp 11808 7488 "GET /static/css/dashboard.css HTTP/1.1" 304 0
INFO 2025-07-25 14:37:14,533 basehttp 11808 7488 "GET /inventory/qr-scanner/ HTTP/1.1" 200 19963
INFO 2025-07-25 14:37:17,561 basehttp 11808 7488 "GET /inventory/low-stock-alerts/ HTTP/1.1" 200 20337
INFO 2025-07-25 14:37:20,625 basehttp 11808 7488 "GET /inventory/ HTTP/1.1" 200 29586
ERROR 2025-07-25 14:37:40,575 middleware 11808 20904 Exception for user gsostaff: OperationalError: no such column: stock_transactions.quantity_change
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
sqlite3.OperationalError: no such column: stock_transactions.quantity_change

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\decorators.py", line 32, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\views.py", line 255, in item_detail
    return render(request, 'inventory/item_detail.html', {'item': item, 'transactions': transactions, 'qr_code': qr_code})
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\defaulttags.py", line 194, in render
    len_values = len(values)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\models\query.py", line 380, in __len__
    self._fetch_all()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\models\query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
django.db.utils.OperationalError: no such column: stock_transactions.quantity_change
ERROR 2025-07-25 14:37:41,397 log 11808 20904 Internal Server Error: /inventory/item/25/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
sqlite3.OperationalError: no such column: stock_transactions.quantity_change

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\decorators.py", line 32, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\views.py", line 255, in item_detail
    return render(request, 'inventory/item_detail.html', {'item': item, 'transactions': transactions, 'qr_code': qr_code})
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\defaulttags.py", line 194, in render
    len_values = len(values)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\models\query.py", line 380, in __len__
    self._fetch_all()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\models\query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
django.db.utils.OperationalError: no such column: stock_transactions.quantity_change
ERROR 2025-07-25 14:37:41,401 basehttp 11808 20904 "GET /inventory/item/25/ HTTP/1.1" 500 235730
INFO 2025-07-25 14:37:44,023 basehttp 11808 23584 "GET /inventory/ HTTP/1.1" 200 29586
ERROR 2025-07-25 14:37:50,137 middleware 11808 23584 Exception for user gsostaff: OperationalError: no such column: stock_transactions.quantity_change
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
sqlite3.OperationalError: no such column: stock_transactions.quantity_change

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\decorators.py", line 32, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\views.py", line 255, in item_detail
    return render(request, 'inventory/item_detail.html', {'item': item, 'transactions': transactions, 'qr_code': qr_code})
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\defaulttags.py", line 194, in render
    len_values = len(values)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\models\query.py", line 380, in __len__
    self._fetch_all()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\models\query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
django.db.utils.OperationalError: no such column: stock_transactions.quantity_change
ERROR 2025-07-25 14:37:50,275 log 11808 23584 Internal Server Error: /inventory/item/25/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
sqlite3.OperationalError: no such column: stock_transactions.quantity_change

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\decorators.py", line 32, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\views.py", line 255, in item_detail
    return render(request, 'inventory/item_detail.html', {'item': item, 'transactions': transactions, 'qr_code': qr_code})
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\defaulttags.py", line 194, in render
    len_values = len(values)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\models\query.py", line 380, in __len__
    self._fetch_all()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\models\query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
django.db.utils.OperationalError: no such column: stock_transactions.quantity_change
ERROR 2025-07-25 14:37:50,283 basehttp 11808 23584 "GET /inventory/item/25/ HTTP/1.1" 500 235730
INFO 2025-07-25 14:38:05,665 basehttp 11808 12280 "GET /inventory/ HTTP/1.1" 200 29586
ERROR 2025-07-25 14:38:13,051 middleware 11808 12280 Exception for user gsostaff: OperationalError: no such column: stock_transactions.quantity_change
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
sqlite3.OperationalError: no such column: stock_transactions.quantity_change

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\decorators.py", line 32, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\views.py", line 255, in item_detail
    return render(request, 'inventory/item_detail.html', {'item': item, 'transactions': transactions, 'qr_code': qr_code})
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\defaulttags.py", line 194, in render
    len_values = len(values)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\models\query.py", line 380, in __len__
    self._fetch_all()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\models\query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
django.db.utils.OperationalError: no such column: stock_transactions.quantity_change
ERROR 2025-07-25 14:38:18,288 log 11808 12280 Internal Server Error: /inventory/item/25/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
sqlite3.OperationalError: no such column: stock_transactions.quantity_change

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\decorators.py", line 32, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\views.py", line 255, in item_detail
    return render(request, 'inventory/item_detail.html', {'item': item, 'transactions': transactions, 'qr_code': qr_code})
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\defaulttags.py", line 194, in render
    len_values = len(values)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\models\query.py", line 380, in __len__
    self._fetch_all()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\models\query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
django.db.utils.OperationalError: no such column: stock_transactions.quantity_change
ERROR 2025-07-25 14:38:18,344 basehttp 11808 12280 "GET /inventory/item/25/ HTTP/1.1" 500 235730
INFO 2025-07-25 14:42:27,444 autoreload 28468 19200 Watching for file changes with StatReloader
INFO 2025-07-25 14:42:35,587 basehttp 11808 24164 "GET / HTTP/1.1" 302 0
INFO 2025-07-25 14:42:35,695 basehttp 11808 24164 "GET /dashboard/gso/ HTTP/1.1" 200 70705
INFO 2025-07-25 14:42:35,834 basehttp 11808 24164 "GET /static/js/dashboard.js HTTP/1.1" 304 0
INFO 2025-07-25 14:42:41,280 basehttp 11808 6704 "GET /inventory/ HTTP/1.1" 200 29586
ERROR 2025-07-25 14:42:43,616 middleware 11808 6704 Exception for user gsostaff: RelatedObjectDoesNotExist: SupplyItem has no qr_code.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\decorators.py", line 32, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\views.py", line 254, in item_detail
    qr_code = item.qr_code
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\models\fields\related_descriptors.py", line 492, in __get__
    raise self.RelatedObjectDoesNotExist(
suptrack.models.SupplyItem.qr_code.RelatedObjectDoesNotExist: SupplyItem has no qr_code.
ERROR 2025-07-25 14:42:43,704 log 11808 6704 Internal Server Error: /inventory/item/25/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\decorators.py", line 32, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\views.py", line 254, in item_detail
    qr_code = item.qr_code
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\models\fields\related_descriptors.py", line 492, in __get__
    raise self.RelatedObjectDoesNotExist(
suptrack.models.SupplyItem.qr_code.RelatedObjectDoesNotExist: SupplyItem has no qr_code.
ERROR 2025-07-25 14:42:43,707 basehttp 11808 6704 "GET /inventory/item/25/ HTTP/1.1" 500 84755
INFO 2025-07-25 14:43:34,446 autoreload 15264 3016 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 14:43:34,871 autoreload 11808 13892 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 14:43:34,877 autoreload 28468 19200 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 14:43:36,141 autoreload 28076 23288 Watching for file changes with StatReloader
INFO 2025-07-25 14:43:36,271 autoreload 10664 21168 Watching for file changes with StatReloader
INFO 2025-07-25 14:43:36,284 autoreload 9052 24112 Watching for file changes with StatReloader
ERROR 2025-07-25 14:45:32,992 middleware 28076 19744 Exception for user gsostaff: OperationalError: no such column: stock_transactions.quantity_change
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: stock_transactions.quantity_change

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\decorators.py", line 32, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\views.py", line 258, in item_detail
    return render(request, 'inventory/item_detail.html', {'item': item, 'transactions': transactions, 'qr_code': qr_code})
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\defaulttags.py", line 199, in render
    len_values = len(values)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\models\query.py", line 366, in __len__
    self._fetch_all()
    ~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\models\query.py", line 1949, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
        chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size
    )
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\models\sql\compiler.py", line 1623, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: stock_transactions.quantity_change
ERROR 2025-07-25 14:45:33,078 log 28076 19744 Internal Server Error: /inventory/item/25/
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: stock_transactions.quantity_change

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\decorators.py", line 32, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\views.py", line 258, in item_detail
    return render(request, 'inventory/item_detail.html', {'item': item, 'transactions': transactions, 'qr_code': qr_code})
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\defaulttags.py", line 199, in render
    len_values = len(values)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\models\query.py", line 366, in __len__
    self._fetch_all()
    ~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\models\query.py", line 1949, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
        chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size
    )
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\models\sql\compiler.py", line 1623, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: stock_transactions.quantity_change
ERROR 2025-07-25 14:45:33,082 basehttp 28076 19744 "GET /inventory/item/25/ HTTP/1.1" 500 227711
INFO 2025-07-25 14:47:51,925 autoreload 9052 24112 C:\Users\<USER>\dev\smartsupply\suptrack\models.py changed, reloading.
INFO 2025-07-25 14:47:52,431 autoreload 28076 23288 C:\Users\<USER>\dev\smartsupply\suptrack\models.py changed, reloading.
INFO 2025-07-25 14:47:54,496 autoreload 23920 23176 Watching for file changes with StatReloader
INFO 2025-07-25 14:47:54,504 autoreload 24056 20432 Watching for file changes with StatReloader
INFO 2025-07-25 14:48:00,996 autoreload 24056 20432 C:\Users\<USER>\dev\smartsupply\suptrack\models.py changed, reloading.
INFO 2025-07-25 14:48:01,202 autoreload 23920 23176 C:\Users\<USER>\dev\smartsupply\suptrack\models.py changed, reloading.
INFO 2025-07-25 14:48:03,300 autoreload 28016 132 Watching for file changes with StatReloader
INFO 2025-07-25 14:48:03,703 autoreload 25456 21636 Watching for file changes with StatReloader
INFO 2025-07-25 14:48:13,373 autoreload 25456 21636 C:\Users\<USER>\dev\smartsupply\suptrack\models.py changed, reloading.
INFO 2025-07-25 14:48:13,879 autoreload 28016 132 C:\Users\<USER>\dev\smartsupply\suptrack\models.py changed, reloading.
INFO 2025-07-25 14:48:14,872 autoreload 26476 20324 Watching for file changes with StatReloader
INFO 2025-07-25 14:48:15,018 autoreload 16124 22936 Watching for file changes with StatReloader
INFO 2025-07-25 14:48:20,554 autoreload 26476 20324 C:\Users\<USER>\dev\smartsupply\suptrack\models.py changed, reloading.
INFO 2025-07-25 14:48:20,676 autoreload 16124 22936 C:\Users\<USER>\dev\smartsupply\suptrack\models.py changed, reloading.
INFO 2025-07-25 14:48:21,600 autoreload 4884 5684 Watching for file changes with StatReloader
INFO 2025-07-25 14:48:21,895 autoreload 19636 10616 Watching for file changes with StatReloader
INFO 2025-07-25 14:49:19,719 autoreload 4884 5684 C:\Users\<USER>\dev\smartsupply\suptrack\models.py changed, reloading.
INFO 2025-07-25 14:49:20,111 autoreload 19636 10616 C:\Users\<USER>\dev\smartsupply\suptrack\models.py changed, reloading.
INFO 2025-07-25 14:49:20,526 autoreload 25172 15644 Watching for file changes with StatReloader
INFO 2025-07-25 14:49:21,005 autoreload 11956 28212 Watching for file changes with StatReloader
INFO 2025-07-25 14:49:26,848 autoreload 25172 15644 C:\Users\<USER>\dev\smartsupply\suptrack\models.py changed, reloading.
INFO 2025-07-25 14:49:27,366 autoreload 11956 28212 C:\Users\<USER>\dev\smartsupply\suptrack\models.py changed, reloading.
INFO 2025-07-25 14:49:28,188 autoreload 12640 12416 Watching for file changes with StatReloader
INFO 2025-07-25 14:49:29,203 autoreload 23088 15388 Watching for file changes with StatReloader
INFO 2025-07-25 14:49:41,768 autoreload 23088 15388 C:\Users\<USER>\dev\smartsupply\suptrack\models.py changed, reloading.
INFO 2025-07-25 14:49:41,915 autoreload 12640 12416 C:\Users\<USER>\dev\smartsupply\suptrack\models.py changed, reloading.
INFO 2025-07-25 14:49:42,797 autoreload 14440 11748 Watching for file changes with StatReloader
INFO 2025-07-25 14:49:42,829 autoreload 11544 22468 Watching for file changes with StatReloader
INFO 2025-07-25 14:49:49,252 autoreload 14440 11748 C:\Users\<USER>\dev\smartsupply\suptrack\models.py changed, reloading.
INFO 2025-07-25 14:49:49,401 autoreload 11544 22468 C:\Users\<USER>\dev\smartsupply\suptrack\models.py changed, reloading.
INFO 2025-07-25 14:49:51,426 autoreload 24184 19016 Watching for file changes with StatReloader
INFO 2025-07-25 14:49:52,139 autoreload 26900 11028 Watching for file changes with StatReloader
ERROR 2025-07-25 14:50:59,861 middleware 24184 5644 Exception for user gsostaff: OperationalError: no such column: stock_transactions.new_quantity_on_hand
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: stock_transactions.new_quantity_on_hand

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\decorators.py", line 32, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\views.py", line 258, in item_detail
    return render(request, 'inventory/item_detail.html', {'item': item, 'transactions': transactions, 'qr_code': qr_code})
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\defaulttags.py", line 199, in render
    len_values = len(values)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\models\query.py", line 366, in __len__
    self._fetch_all()
    ~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\models\query.py", line 1949, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
        chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size
    )
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\models\sql\compiler.py", line 1623, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: stock_transactions.new_quantity_on_hand
ERROR 2025-07-25 14:50:59,955 log 24184 5644 Internal Server Error: /inventory/item/25/
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: stock_transactions.new_quantity_on_hand

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\decorators.py", line 32, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\views.py", line 258, in item_detail
    return render(request, 'inventory/item_detail.html', {'item': item, 'transactions': transactions, 'qr_code': qr_code})
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\defaulttags.py", line 199, in render
    len_values = len(values)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\models\query.py", line 366, in __len__
    self._fetch_all()
    ~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\models\query.py", line 1949, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
        chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size
    )
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\models\sql\compiler.py", line 1623, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: stock_transactions.new_quantity_on_hand
ERROR 2025-07-25 14:50:59,959 basehttp 24184 5644 "GET /inventory/item/25/ HTTP/1.1" 500 227861
INFO 2025-07-25 14:56:11,244 autoreload 10328 14732 Watching for file changes with StatReloader
INFO 2025-07-25 14:56:49,516 basehttp 24184 26840 "GET /inventory/item/25/ HTTP/1.1" 302 0
INFO 2025-07-25 14:56:49,568 basehttp 24184 26840 "GET /login/?next=/inventory/item/25/ HTTP/1.1" 200 13301
INFO 2025-07-25 14:56:49,757 basehttp 24184 26840 "GET /static/css/dashboard.css HTTP/1.1" 304 0
INFO 2025-07-25 14:56:49,757 basehttp 24184 17208 "GET /static/js/dashboard.js HTTP/1.1" 304 0
INFO 2025-07-25 14:57:53,814 basehttp 24184 19164 "GET /inventory/item/25/ HTTP/1.1" 200 24656
INFO 2025-07-25 14:57:54,122 basehttp 24184 19164 "GET /static/css/dashboard.css HTTP/1.1" 304 0
INFO 2025-07-25 14:57:54,129 basehttp 24184 20408 "GET /static/js/dashboard.js HTTP/1.1" 304 0
INFO 2025-07-25 14:58:21,761 basehttp 24184 21636 "POST /inventory/item/25/issue/ HTTP/1.1" 302 0
INFO 2025-07-25 14:58:21,818 basehttp 24184 21636 "GET /inventory/item/25/ HTTP/1.1" 200 25157
INFO 2025-07-25 14:58:32,810 basehttp 24184 7768 "POST /inventory/item/25/issue/ HTTP/1.1" 302 0
INFO 2025-07-25 14:58:32,873 basehttp 24184 7768 "GET /inventory/item/25/ HTTP/1.1" 200 26502
INFO 2025-07-25 14:58:46,254 basehttp 24184 10680 "GET /inventory/item/25/edit/ HTTP/1.1" 200 21167
INFO 2025-07-25 14:58:51,710 basehttp 24184 11560 "GET /inventory/low-stock-alerts/ HTTP/1.1" 200 20337
INFO 2025-07-25 14:58:53,662 basehttp 24184 11560 "GET /inventory/ HTTP/1.1" 200 29662
INFO 2025-07-25 14:59:01,724 basehttp 24184 19376 "GET /inventory/item/25/ HTTP/1.1" 200 25984
INFO 2025-07-25 14:59:18,448 basehttp 24184 7356 "GET /inventory/ HTTP/1.1" 200 29662
INFO 2025-07-25 14:59:37,049 basehttp 24184 1248 "GET /supply-request/ HTTP/1.1" 200 33540
INFO 2025-07-25 14:59:46,765 basehttp 24184 2580 "GET /inventory/ HTTP/1.1" 200 29662
INFO 2025-07-25 15:00:42,936 autoreload 10328 14732 C:\Users\<USER>\dev\smartsupply\suptrack\migrations\0004_manual_rename_quantity.py changed, reloading.
INFO 2025-07-25 15:00:44,854 autoreload 27264 6432 Watching for file changes with StatReloader
