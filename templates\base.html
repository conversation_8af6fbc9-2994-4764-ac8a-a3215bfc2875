{% load static %}
{% load inventory_tags %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Smart Supply Management{% endblock %}</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Alpine.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    
    <!-- HTMX -->
    <script src="https://unpkg.com/htmx.org@1.9.6"></script>
    
    <!-- Unpoly -->
    <script src="https://unpkg.com/unpoly@3.0.0/unpoly.min.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/unpoly@3.0.0/unpoly.min.css">
    
    <!-- Custom styles -->
    <style>
        [x-cloak] { display: none !important; }
    </style>
    
    <!-- Dashboard CSS -->
    <link rel="stylesheet" href="{% static 'css/dashboard.css' %}">
    
    {% block extra_css %}{% endblock %}
</head>
<body class="bg-gray-100 min-h-screen">
    <div x-data="{ sidebarOpen: window.innerWidth >= 1024 }" class="flex h-screen overflow-hidden bg-gray-100">
        <!-- Mobile sidebar backdrop -->
        <div x-show="sidebarOpen && window.innerWidth < 1024" 
             x-transition:enter="transition-opacity ease-linear duration-300" 
             x-transition:enter-start="opacity-0" 
             x-transition:enter-end="opacity-100" 
             x-transition:leave="transition-opacity ease-linear duration-300" 
             x-transition:leave-start="opacity-100" 
             x-transition:leave-end="opacity-0" 
             class="fixed inset-0 z-20 bg-gray-600 bg-opacity-75 lg:hidden"
             @click="sidebarOpen = false"
             x-cloak></div>
        
        <!-- Sidebar -->
        <div id="sidebar"
             :class="{'hidden': !sidebarOpen && window.innerWidth >= 1024, '-translate-x-full': !sidebarOpen && window.innerWidth < 1024}"
             x-transition:enter="transition ease-in-out duration-300 transform" 
             x-transition:enter-start="-translate-x-full" 
             x-transition:enter-end="translate-x-0" 
             x-transition:leave="transition ease-in-out duration-300 transform" 
             x-transition:leave-start="translate-x-0" 
             x-transition:leave-end="-translate-x-full" 
             class="fixed inset-y-0 left-0 z-30 w-64 overflow-y-auto bg-blue-800 transform transition-transform duration-300 lg:static lg:inset-0 lg:translate-x-0 lg:bg-blue-800"
             x-cloak>
            
            <div class="flex items-center justify-between px-4 py-6">
                <div class="flex items-center">
                    <span class="text-white text-2xl font-semibold">Smart Supply</span>
                </div>
                <button id="close-sidebar" @click="sidebarOpen = false" class="text-white focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white md:hidden">
                    <span class="sr-only">Close sidebar</span>
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            
            <nav class="mt-5 px-2" x-data="{ openDropdown: '' }">
                <!-- Dashboard Link -->
                <a href="{% url 'suptrack:dashboard' %}"
                   class="group flex items-center px-2 py-2 text-base leading-6 font-medium rounded-md transition ease-in-out duration-150
                          {% if request.resolver_match.url_name == 'dashboard' %}
                              bg-blue-900 text-white
                          {% else %}
                              text-blue-100 hover:bg-blue-700 hover:text-white
                          {% endif %}">
                    <svg class="mr-4 h-6 w-6 {% if request.resolver_match.url_name == 'dashboard' %}text-blue-300{% else %}text-blue-400 group-hover:text-blue-300{% endif %}"
                         fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                    </svg>
                    Dashboard
                </a>

                <!-- Department User Links -->
                {% if request.user.is_authenticated and request.user.userprofile.role == 'department_user' %}
                <div class="mt-2">
                    <h3 class="px-2 text-xs font-semibold text-blue-300 uppercase tracking-wider">Supply Requests</h3>
                    <div class="mt-1 space-y-1">
                        <a href="{% url 'suptrack:create_supply_request' %}"
                           class="group flex items-center px-2 py-2 text-sm leading-5 font-medium rounded-md transition ease-in-out duration-150
                                  {% if request.resolver_match.url_name == 'create_supply_request' %}
                                      bg-blue-900 text-white
                                  {% else %}
                                      text-blue-100 hover:bg-blue-700 hover:text-white
                                  {% endif %}">
                            <svg class="mr-3 h-5 w-5 {% if request.resolver_match.url_name == 'create_supply_request' %}text-blue-300{% else %}text-blue-400 group-hover:text-blue-300{% endif %}"
                                 fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            New Request
                        </a>
                        <a href="{% url 'suptrack:supply_request_list' %}"
                           class="group flex items-center px-2 py-2 text-sm leading-5 font-medium rounded-md transition ease-in-out duration-150
                                  {% if request.resolver_match.url_name == 'supply_request_list' %}
                                      bg-blue-900 text-white
                                  {% else %}
                                      text-blue-100 hover:bg-blue-700 hover:text-white
                                  {% endif %}">
                            <svg class="mr-3 h-5 w-5 {% if request.resolver_match.url_name == 'supply_request_list' %}text-blue-300{% else %}text-blue-400 group-hover:text-blue-300{% endif %}"
                                 fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                            </svg>
                            My Requests
                        </a>
                    </div>
                </div>
                {% endif %}

                <!-- GSO Staff Links -->
                {% if request.user.is_authenticated and request.user.userprofile.role == 'gso_staff' %}
                <div class="mt-2">
                    <h3 class="px-2 text-xs font-semibold text-blue-300 uppercase tracking-wider">GSO Management</h3>
                    <div class="mt-1 space-y-1">
                        <!-- Supply Requests Dropdown -->
                        <div>
                            <button @click="openDropdown = openDropdown === 'requests' ? '' : 'requests'"
                                    class="w-full text-left group flex items-center px-2 py-2 text-sm leading-5 font-medium rounded-md transition ease-in-out duration-150
                                           {% if 'supply_request' in request.resolver_match.url_name or 'review_supply_request' in request.resolver_match.url_name %}
                                               bg-blue-900 text-white
                                           {% else %}
                                               text-blue-100 hover:bg-blue-700 hover:text-white
                                           {% endif %}">
                                <svg class="mr-3 h-5 w-5 {% if 'supply_request' in request.resolver_match.url_name %}text-blue-300{% else %}text-blue-400 group-hover:text-blue-300{% endif %}"
                                     fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                                </svg>
                                Supply Requests
                                <svg :class="{'transform rotate-180': openDropdown === 'requests'}"
                                     class="ml-auto h-4 w-4 transform transition-transform duration-150 text-blue-400"
                                     fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                </svg>
                            </button>
                            <div x-show="openDropdown === 'requests'"
                                 x-transition:enter="transition ease-out duration-100"
                                 x-transition:enter-start="transform opacity-0 scale-95"
                                 x-transition:enter-end="transform opacity-100 scale-100"
                                 x-transition:leave="transition ease-in duration-75"
                                 x-transition:leave-start="transform opacity-100 scale-100"
                                 x-transition:leave-end="transform opacity-0 scale-95"
                                 class="pl-8 mt-1 space-y-1">
                                <a href="{% url 'suptrack:supply_request_list' %}"
                                   class="block px-2 py-2 text-sm leading-5 font-medium rounded-md text-blue-100 hover:bg-blue-700 hover:text-white transition ease-in-out duration-150">
                                    All Requests
                                </a>
                                <a href="#"
                                   class="block px-2 py-2 text-sm leading-5 font-medium rounded-md text-blue-100 hover:bg-blue-700 hover:text-white transition ease-in-out duration-150">
                                    Pending Approval
                                </a>
                            </div>
                        </div>

                        <!-- Inventory Management -->
                        <a href="{% url 'suptrack:inventory_dashboard' %}"
                           class="group flex items-center px-2 py-2 text-sm leading-5 font-medium rounded-md transition ease-in-out duration-150
                                  {% if request.resolver_match.url_name == 'inventory_dashboard' %}
                                      bg-blue-900 text-white
                                  {% else %}
                                      text-blue-100 hover:bg-blue-700 hover:text-white
                                  {% endif %}">
                            <svg class="mr-3 h-5 w-5 {% if request.resolver_match.url_name == 'inventory_dashboard' %}text-blue-300{% else %}text-blue-400 group-hover:text-blue-300{% endif %}"
                                 fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                            </svg>
                            Inventory
                        </a>

                        <!-- Low Stock Alerts -->
                        <a href="{% url 'suptrack:low_stock_alert' %}"
                           class="group flex items-center px-2 py-2 text-sm leading-5 font-medium rounded-md transition ease-in-out duration-150
                                  {% if request.resolver_match.url_name == 'low_stock_alert' %}
                                      bg-blue-900 text-white
                                  {% else %}
                                      text-blue-100 hover:bg-blue-700 hover:text-white
                                  {% endif %}">
                            <svg class="mr-3 h-5 w-5 {% if request.resolver_match.url_name == 'low_stock_alert' %}text-blue-300{% else %}text-blue-400 group-hover:text-blue-300{% endif %}"
                                 fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                            Low Stock Alerts
                        </a>

                        <!-- QR Scanner -->
                        <a href="{% url 'suptrack:qr_scanner' %}"
                           class="group flex items-center px-2 py-2 text-sm leading-5 font-medium rounded-md transition ease-in-out duration-150
                                  {% if request.resolver_match.url_name == 'qr_scanner' %}
                                      bg-blue-900 text-white
                                  {% else %}
                                      text-blue-100 hover:bg-blue-700 hover:text-white
                                  {% endif %}">
                            <svg class="mr-3 h-5 w-5 {% if request.resolver_match.url_name == 'qr_scanner' %}text-blue-300{% else %}text-blue-400 group-hover:text-blue-300{% endif %}"
                                 fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z"></path>
                            </svg>
                            QR Scanner
                        </a>
                    </div>
                </div>
                {% endif %}

                <!-- User Profile Section -->
                {% if request.user.is_authenticated %}
                <div class="mt-8 pt-6 border-t border-blue-700">
                    <div class="px-2 mb-4">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="h-8 w-8 rounded-full bg-blue-600 flex items-center justify-center">
                                    <span class="text-sm font-medium text-white">{{ request.user.first_name.0|default:request.user.username.0|upper }}</span>
                                </div>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-white">{{ request.user.get_full_name|default:request.user.username }}</p>
                                <p class="text-xs text-blue-300">{% if request.user.userprofile %}{{ request.user.userprofile.get_role_display }}{% else %}User{% endif %}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Logout Link -->
                    <a href="{% url 'suptrack:logout' %}"
                       class="group flex items-center px-2 py-2 text-sm leading-5 font-medium rounded-md text-blue-100 hover:bg-red-600 hover:text-white transition ease-in-out duration-150">
                        <svg class="mr-3 h-5 w-5 text-blue-400 group-hover:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H3"></path>
                        </svg>
                        Logout
                    </a>
                </div>
                {% endif %}
            </nav>
        </div>
        
        <!-- Main content -->
        <div id="main-content" class="flex-1 flex flex-col overflow-hidden transition-all duration-300" :class="{'lg:ml-0': !sidebarOpen && window.innerWidth >= 1024}">
            <!-- Top header -->
            <header class="bg-white shadow-sm lg:static lg:overflow-y-visible">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="relative flex justify-between h-16">
                        <div class="flex">
                            <!-- Mobile menu button -->
                            <button id="sidebar-toggle" @click="sidebarOpen = !sidebarOpen" class="px-4 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500">
                                <span class="sr-only">Toggle sidebar</span>
                                <svg x-show="!sidebarOpen" class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                                </svg>
                                <svg x-show="sidebarOpen" class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                            
                            <!-- Breadcrumb -->
                            <div class="flex-shrink-0 flex items-center ml-4 lg:ml-0">
                                <h1 class="text-2xl font-semibold text-gray-900">{% block header_title %}Dashboard{% endblock %}</h1>
                            </div>
                        </div>
                        
                        <!-- User profile dropdown -->
                        <div class="flex items-center">
                            <div class="ml-3 relative" x-data="{ open: false }">
                                <div>
                                    <button @click="open = !open" class="max-w-xs bg-white flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" id="user-menu" aria-haspopup="true">
                                        <span class="sr-only">Open user menu</span>
                                        <span class="inline-block h-8 w-8 rounded-full overflow-hidden bg-gray-100">
                                            <svg class="h-full w-full text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                                                <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z"></path>
                                            </svg>
                                        </span>
                                    </button>
                                </div>
                                
                                <div x-show="open" 
                                     x-transition:enter="transition ease-out duration-100" 
                                     x-transition:enter-start="transform opacity-0 scale-95" 
                                     x-transition:enter-end="transform opacity-100 scale-100" 
                                     x-transition:leave="transition ease-in duration-75" 
                                     x-transition:leave-start="transform opacity-100 scale-100" 
                                     x-transition:leave-end="transform opacity-0 scale-95" 
                                     class="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg py-1 bg-white ring-1 ring-black ring-opacity-5" 
                                     role="menu" 
                                     aria-orientation="vertical" 
                                     aria-labelledby="user-menu"
                                     @click.away="open = false"
                                     x-cloak>
                                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem">Your Profile</a>
                                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem">Settings</a>
                                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem">Sign out</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- Main content area -->
            <main class="flex-1 overflow-y-auto focus:outline-none">
                <div class="py-6">
                    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        {% block breadcrumbs %}
                        <nav class="flex" aria-label="Breadcrumb">
                            <ol class="flex items-center space-x-4">
                                <li>
                                    <div>
                                        <a href="{% url 'suptrack:dashboard' %}" class="text-gray-400 hover:text-gray-500">
                                            <svg class="flex-shrink-0 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
                                            </svg>
                                            <span class="sr-only">Home</span>
                                        </a>
                                    </div>
                                </li>
                                {% block breadcrumb_items %}{% endblock %}
                            </ol>
                        </nav>
                        {% endblock %}
                    </div>
                    
                    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mt-6">
                        {% if messages %}
                            <div class="mb-4">
                                {% for message in messages %}
                                    <div class="p-4 rounded-md {% if message.tags == 'success' %}bg-green-100 border border-green-400 text-green-700{% elif message.tags == 'error' %}bg-red-100 border border-red-400 text-red-700{% else %}bg-blue-100 border border-blue-400 text-blue-700{% endif %}" role="alert">
                                        <p class="font-bold">{% if message.tags == 'success' %}Success{% elif message.tags == 'error' %}Error{% else %}Info{% endif %}</p>
                                        <p>{{ message }}</p>
                                    </div>
                                {% endfor %}
                            </div>
                        {% endif %}


{% block content %}{% endblock %}
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <!-- Dashboard JavaScript -->
    <script src="{% static 'js/dashboard.js' %}"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>