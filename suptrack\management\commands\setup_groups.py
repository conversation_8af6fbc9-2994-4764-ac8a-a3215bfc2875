from django.core.management.base import BaseCommand
from django.contrib.auth.models import Group, User

class Command(BaseCommand):
    help = 'Creates the GSO Staff group and assigns the gsostaff user to it.'

    def handle(self, *args, **options):
        # Create the GSO Staff group if it doesn't exist
        gso_group, created = Group.objects.get_or_create(name='GSO Staff')
        if created:
            self.stdout.write(self.style.SUCCESS('Successfully created "GSO Staff" group.'))
        else:
            self.stdout.write('"GSO Staff" group already exists.')

        # Find the gsostaff user and add them to the GSO Staff group
        try:
            gso_user = User.objects.get(username='gsostaff')
            # Add user to the group if not already a member
            if gso_group not in gso_user.groups.all():
                gso_user.groups.add(gso_group)
                self.stdout.write(self.style.SUCCESS(f'Successfully added user "{gso_user.username}" to the "GSO Staff" group.'))
            else:
                self.stdout.write(f'User "{gso_user.username}" is already in the "GSO Staff" group.')

            # Ensure the user is active and set a known password
            gso_user.is_active = True
            gso_user.set_password('password123')
            gso_user.save()
            self.stdout.write(self.style.SUCCESS(f'User "{gso_user.username}" is active and password has been set.'))
        except User.DoesNotExist:
            self.stdout.write(self.style.ERROR('User "gsostaff" does not exist. Please create the user first.'))
